package cn.iocoder.yudao.module.demo.dal.mysql.iotwarning;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.demo.controller.admin.iotwarning.vo.IotWarningPageReqVO;
import cn.iocoder.yudao.module.demo.dal.dataobject.iotwarning.IotWarningDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 物联平台设备告警数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface IotWarningMapper extends BaseMapperX<IotWarningDO> {

    default PageResult<IotWarningDO> selectPage(IotWarningPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<IotWarningDO>()
                .eqIfPresent(IotWarningDO::getNumber, reqVO.getNumber())
                .eqIfPresent(IotWarningDO::getType, reqVO.getType())
                .eqIfPresent(IotWarningDO::getCenter, reqVO.getCenter())
                .eqIfPresent(IotWarningDO::getWarehouse, reqVO.getWarehouse())
                .eqIfPresent(IotWarningDO::getContent, reqVO.getContent())
                .betweenIfPresent(IotWarningDO::getTime, reqVO.getTime())
                .eqIfPresent(IotWarningDO::getHandleStatus, reqVO.getHandleStatus())
                .betweenIfPresent(IotWarningDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(IotWarningDO::getCreateTime));
    }

    default Long selectCount(IotWarningPageReqVO reqVO) {
        return selectCount(new LambdaQueryWrapperX<IotWarningDO>()
                .eqIfPresent(IotWarningDO::getNumber, reqVO.getNumber())
                .eqIfPresent(IotWarningDO::getType, reqVO.getType())
                .eqIfPresent(IotWarningDO::getCenter, reqVO.getCenter())
                .eqIfPresent(IotWarningDO::getWarehouse, reqVO.getWarehouse())
                .eqIfPresent(IotWarningDO::getContent, reqVO.getContent())
                .betweenIfPresent(IotWarningDO::getTime, reqVO.getTime())
                .inIfPresent(IotWarningDO::getDeptId, reqVO.getDeptIdList())
                .eqIfPresent(IotWarningDO::getHandleStatus, reqVO.getHandleStatus())
                .betweenIfPresent(IotWarningDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(IotWarningDO::getId));
    }

}
