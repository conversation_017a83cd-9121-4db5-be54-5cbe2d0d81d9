<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.demo.dal.mysql.materialaccept.MaterialAcceptMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 物资验收信息与流程节点关联查询结果映射 -->
    <resultMap id="MaterialAcceptWithFlowResultMap" type="cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo.MaterialAcceptWithFlowRespVO">
        <!-- 物资验收信息字段 -->
        <id property="id" column="id"/>
        <result property="acceptNo" column="accept_no"/>
        <result property="unitName" column="unit_name"/>
        <result property="unitId" column="unit_id"/>
        <result property="materialType" column="material_type"/>
        <result property="materialCode" column="material_code"/>
        <result property="supplier" column="supplier"/>
        <result property="visualCheck" column="visual_check"/>
        <result property="principleCheck" column="principle_check"/>
        <result property="problemOpinion" column="problem_opinion"/>
        <result property="usageDesc" column="usage_desc"/>
        <result property="materialSituation" column="material_situation"/>
        <result property="qualityInspection" column="quality_inspection"/>
        <result property="acceptDate" column="accept_date"/>
        <result property="acceptUser" column="accept_user"/>
        <result property="signArea" column="sign_area"/>
        <result property="fileIds" column="file_ids"/>
        <result property="createTime" column="create_time"/>
        <result property="materialBelong" column="material_belong"/>
        <result property="inboundNo" column="inbound_no"/>
        <result property="inboundType" column="inbound_type"/>
        <result property="outboundType" column="outbound_type"/>
        <result property="relatedNo" column="related_no"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="expectedInboundTime" column="expected_inbound_time"/>
        <result property="remark" column="remark"/>
        <result property="inboundOperator" column="inbound_operator"/>
        <result property="flow" column="flow"/>
        <result property="createDeptId" column="create_dept_id"/>
        <result property="type" column="type"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="inspectionStatus" column="inspection_status"/>
        <result property="materialClass" column="material_class"/>
        <result property="spec" column="spec"/>
        <result property="unit" column="unit"/>
        <result property="quantity" column="quantity"/>
        <result property="originId" column="origin_id"/>
        <result property="dispatchOrderId" column="dispatch_order_id"/>
        <result property="dispatchOrderNo" column="dispatch_order_no"/>
        <result property="receiveUnit" column="receive_unit"/>
        <result property="receiveId" column="receive_id"/>
        <result property="unitAddress" column="unit_address"/>
        <result property="contactPerson" column="contact_person"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="materialName" column="material_name"/>
        <result property="originalLocation" column="original_location"/>
        <result property="location" column="location"/>
        <result property="locationId" column="location_id"/>
        <result property="newLocation" column="new_location"/>
        <result property="planTime" column="plan_time"/>
        <result property="checkRange" column="check_range"/>
        <result property="proFactoryName" column="pro_factory_name"/>
        <result property="proFactoryId" column="pro_factory_id"/>
        <result property="types" column="types"/>
        <result property="inspectionWarehouseIds" column="inspection_warehouse_ids"/>
        <result property="inspectionType" column="inspection_type"/>
        <result property="fileList" column="file_list"/>
        <result property="thirdResultFlag" column="third_result_flag"/>
        <result property="dataSource" column="data_source"/>

        <!-- 流程节点字段 -->
        <result property="flowNodeId" column="flow_node_id"/>
        <result property="roleId" column="role_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="businessId" column="business_id"/>
        <result property="isPass" column="is_pass"/>
        <result property="auditPerson" column="audit_person"/>
        <result property="auditSign" column="audit_sign"/>
        <result property="auditReason" column="audit_reason"/>
    </resultMap>

    <!-- 关联查询物资验收信息和待处理的流程节点 -->
    <select id="selectPageWithFlow" resultMap="MaterialAcceptWithFlowResultMap">
        SELECT
            m.id,
            m.accept_no,
            m.unit_name,
            m.unit_id,
            m.material_type,
            m.supplier,
            m.visual_check,
            m.principle_check,
            m.problem_opinion,
            m.usage_desc,
            m.material_situation,
            m.quality_inspection,
            m.accept_date,
            m.accept_user,
            m.file_ids,
            m.create_time,
            m.material_belong,
            m.inbound_no,
            m.inbound_type,
            m.outbound_type,
            m.related_no,
            m.warehouse_id,
            m.warehouse_name,
            m.expected_inbound_time,
            m.remark,
            m.inbound_operator,
            m.flow,
            m.create_dept_id,
            m.type,
            m.creator,
            m.updater,
            m.update_time,
            m.deleted,
            m.audit_status,
            m.inspection_status,
            m.material_class,
            m.spec,
            m.unit,
            m.quantity,
            m.origin_id,
            m.dispatch_order_id,
            m.dispatch_order_no,
            m.receive_unit,
            m.receive_id,
            m.unit_address,
            m.contact_person,
            m.contact_phone,
            m.material_name,
            m.original_location,
            m.location,
            m.location_id,
            m.new_location,
            m.plan_time,
            m.check_range,
            m.pro_factory_name,
            m.pro_factory_id,
            m.types,
            m.inspection_warehouse_ids,
            m.inspection_type,
            t.id AS flow_node_id,
            t.role_id,
            t.dept_id,
            t.order_no,
            t.business_id,
            t.is_pass,
            t.audit_person,
            t.audit_sign,
            t.audit_reason,
            m.material_name,
            m.material_code,
            m.original_location,
            m.location,
            m.location_id,
            m.new_location,
            m.plan_time,
            m.check_range,
            m.third_result_flag,
            m.data_source
        FROM t_material_accept m
        LEFT JOIN (
            SELECT t1.*
            FROM t_biz_flow_node t1
            JOIN (
                SELECT business_id,
                       CASE
                           -- 优先选择驳回的节点
                           WHEN EXISTS (SELECT 1 FROM t_biz_flow_node WHERE business_id = t.business_id AND is_pass = 0 AND deleted = 0)
                               THEN (SELECT MIN(order_no) FROM t_biz_flow_node WHERE business_id = t.business_id AND is_pass = 0 AND deleted = 0)
                           -- 其次选择待处理的节点
                           WHEN EXISTS (SELECT 1 FROM t_biz_flow_node WHERE business_id = t.business_id AND is_pass IS NULL AND deleted = 0)
                               THEN (SELECT MIN(order_no) FROM t_biz_flow_node WHERE business_id = t.business_id AND is_pass IS NULL AND deleted = 0)
                           -- 如果既没有驳回也没有待处理，返回NULL
                           ELSE NULL
                       END AS min_order_no
                FROM (SELECT DISTINCT business_id FROM t_biz_flow_node WHERE deleted = 0 AND business_id IS NOT NULL) t
            ) t2
              ON t1.business_id = t2.business_id
             AND t1.order_no = t2.min_order_no
            WHERE t1.deleted = 0
              AND t1.business_id IS NOT NULL
        ) t
          ON m.id = t.business_id
        WHERE m.deleted = 0
        <if test="reqVO.acceptNo != null and reqVO.acceptNo != ''">
            AND m.accept_no LIKE CONCAT('%', #{reqVO.acceptNo}, '%')
        </if>
        <if test="reqVO.flow != null and reqVO.flow != ''">
            AND (t.order_no &gt;= #{reqVO.flow} or audit_status= 4)
        </if>
        <if test="reqVO.unitName != null and reqVO.unitName != ''">
            AND m.unit_name LIKE CONCAT('%', #{reqVO.unitName}, '%')
        </if>
        <if test="reqVO.unitId != null">
            AND m.unit_id = #{reqVO.unitId}
        </if>
        <if test="reqVO.materialType != null and reqVO.materialType != ''">
            AND m.material_type = #{reqVO.materialType}
        </if>
        <if test="reqVO.supplier != null and reqVO.supplier != ''">
            AND m.supplier LIKE CONCAT('%', #{reqVO.supplier}, '%')
        </if>
        <if test="reqVO.visualCheck != null and reqVO.visualCheck != ''">
            AND m.visual_check = #{reqVO.visualCheck}
        </if>
        <if test="reqVO.principleCheck != null and reqVO.principleCheck != ''">
            AND m.principle_check = #{reqVO.principleCheck}
        </if>
        <if test="reqVO.problemOpinion != null and reqVO.problemOpinion != ''">
            AND m.problem_opinion = #{reqVO.problemOpinion}
        </if>
        <if test="reqVO.usageDesc != null and reqVO.usageDesc != ''">
            AND m.usage_desc = #{reqVO.usageDesc}
        </if>
        <if test="reqVO.materialSituation != null and reqVO.materialSituation != ''">
            AND m.material_situation = #{reqVO.materialSituation}
        </if>
        <if test="reqVO.qualityInspection != null and reqVO.qualityInspection != ''">
            AND m.quality_inspection = #{reqVO.qualityInspection}
        </if>
        <if test="reqVO.acceptDate != null and reqVO.acceptDate.length == 2">
            AND m.accept_date BETWEEN #{reqVO.acceptDate[0]} AND #{reqVO.acceptDate[1]}
        </if>
        <if test="reqVO.acceptUser != null and reqVO.acceptUser != ''">
            AND m.accept_user = #{reqVO.acceptUser}
        </if>
        <if test="reqVO.signArea != null and reqVO.signArea != ''">
            AND m.sign_area = #{reqVO.signArea}
        </if>
        <if test="reqVO.fileIds != null and reqVO.fileIds != ''">
            AND m.file_ids = #{reqVO.fileIds}
        </if>
        <if test="reqVO.createTime != null and reqVO.createTime.length == 2">
            AND m.create_time BETWEEN #{reqVO.createTime[0]} AND #{reqVO.createTime[1]}
        </if>
        <if test="reqVO.materialBelong != null and reqVO.materialBelong != ''">
            AND m.material_belong = #{reqVO.materialBelong}
        </if>
        <if test="reqVO.inboundNo != null and reqVO.inboundNo != ''">
            AND m.inbound_no = #{reqVO.inboundNo}
        </if>
        <if test="reqVO.inboundType != null and reqVO.inboundType != ''">
            AND m.inbound_type = #{reqVO.inboundType}
        </if>
        <if test="reqVO.outboundType != null and reqVO.outboundType != ''">
            AND m.outbound_type = #{reqVO.outboundType}
        </if>
        <if test="reqVO.relatedNo != null and reqVO.relatedNo != ''">
            AND m.related_no = #{reqVO.relatedNo}
        </if>
        <if test="reqVO.warehouseId != null">
            AND m.warehouse_id = #{reqVO.warehouseId}
        </if>
        <if test="reqVO.warehouseName != null and reqVO.warehouseName != ''">
            AND m.warehouse_name LIKE CONCAT('%', #{reqVO.warehouseName}, '%')
        </if>
        <if test="reqVO.expectedInboundTime != null and reqVO.expectedInboundTime.length == 2">
            AND m.expected_inbound_time BETWEEN #{reqVO.expectedInboundTime[0]} AND #{reqVO.expectedInboundTime[1]}
        </if>
        <if test="reqVO.remark != null and reqVO.remark != ''">
            AND m.remark LIKE CONCAT('%', #{reqVO.remark}, '%')
        </if>
        <if test="reqVO.inboundOperator != null and reqVO.inboundOperator != ''">
            AND m.inbound_operator = #{reqVO.inboundOperator}
        </if>
        <if test="reqVO.type != null and reqVO.type != ''">
            AND m.type = #{reqVO.type}
        </if>
        <if test="reqVO.thirdResultFlag != null and reqVO.thirdResultFlag != ''">
            AND m.third_result_flag = #{reqVO.thirdResultFlag}
        </if>
        <if test="reqVO.auditStatus != null">
            AND (
            <if test="reqVO.auditStatus == 1 or reqVO.auditStatus == 2">
                (
                (m.audit_status = 1 AND t.dept_id = #{loginUserDeptId} AND t.role_id IN
                <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
                )
                OR
                (m.audit_status = 1 AND NOT (t.dept_id = #{loginUserDeptId} AND t.role_id IN
                <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
                ))
                )
            </if>
            <if test="reqVO.auditStatus != 1 and reqVO.auditStatus != 2">
                m.audit_status = #{reqVO.auditStatus}
            </if>
            )
        </if>
        <if test="reqVO.inspectionStatus != null">
            AND m.inspection_status = #{reqVO.inspectionStatus}
        </if>
        <if test="reqVO.inspectionType != null and reqVO.inspectionType != ''">
            AND m.inspection_type = #{reqVO.inspectionType}
        </if>
        <if test="reqVO.materialClass != null and reqVO.materialClass != ''">
            AND m.material_class = #{reqVO.materialClass}
        </if>
        <if test="reqVO.spec != null and reqVO.spec != ''">
            AND m.spec LIKE CONCAT('%', #{reqVO.spec}, '%')
        </if>
        <if test="reqVO.unit != null and reqVO.unit != ''">
            AND m.unit = #{reqVO.unit}
        </if>
        <if test="reqVO.quantity != null">
            AND m.quantity = #{reqVO.quantity}
        </if>
        <if test="reqVO.originId != null">
            AND m.origin_id = #{reqVO.originId}
        </if>
        <if test="reqVO.dispatchOrderId != null">
            AND m.dispatch_order_id = #{reqVO.dispatchOrderId}
        </if>
        <if test="reqVO.dispatchOrderNo != null and reqVO.dispatchOrderNo != ''">
            AND m.dispatch_order_no = #{reqVO.dispatchOrderNo}
        </if>
        <if test="reqVO.receiveUnit != null and reqVO.receiveUnit != ''">
            AND m.receive_unit LIKE CONCAT('%', #{reqVO.receiveUnit}, '%')
        </if>
        <if test="reqVO.receiveId != null">
            AND m.receive_id = #{reqVO.receiveId}
        </if>
        <if test="reqVO.createDeptId != null">
            AND m.create_dept_id = #{reqVO.createDeptId}
        </if>
        <if test="reqVO.unitAddress != null and reqVO.unitAddress != ''">
            AND m.unit_address LIKE CONCAT('%', #{reqVO.unitAddress}, '%')
        </if>
        <if test="reqVO.contactPerson != null and reqVO.contactPerson != ''">
            AND m.contact_person LIKE CONCAT('%', #{reqVO.contactPerson}, '%')
        </if>
        <if test="reqVO.contactPhone != null and reqVO.contactPhone != ''">
            AND m.contact_phone LIKE CONCAT('%', #{reqVO.contactPhone}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND m.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.originalLocation != null and reqVO.originalLocation != ''">
            AND m.original_location LIKE CONCAT('%', #{reqVO.originalLocation}, '%')
        </if>
        <if test="reqVO.location != null and reqVO.location != ''">
            AND m.location LIKE CONCAT('%', #{reqVO.location}, '%')
        </if>
        <if test="reqVO.locationId != null">
            AND m.location_id = #{reqVO.locationId}
        </if>
        <if test="reqVO.newLocation != null and reqVO.newLocation != ''">
            AND m.new_location LIKE CONCAT('%', #{reqVO.newLocation}, '%')
        </if>
        <if test="reqVO.checkRange != null and reqVO.checkRange != ''">
            AND m.check_range = #{reqVO.checkRange}
        </if>
        ORDER BY m.id DESC
        <if test="reqVO.pageSize != null">
            <if test="reqVO.offset != null">
                OFFSET #{reqVO.offset} ROWS FETCH NEXT #{reqVO.pageSize} ROWS ONLY
            </if>
        </if>
    </select>

    <!-- 计算总记录数，用于分页 -->
    <select id="selectCountWithFlow" resultType="java.lang.Long">
        SELECT COUNT(m.id)
        FROM t_material_accept m
        LEFT JOIN (
            SELECT t1.*
            FROM t_biz_flow_node t1
            JOIN (
                SELECT business_id,
                       CASE
                           -- 优先选择驳回的节点
                           WHEN EXISTS (SELECT 1 FROM t_biz_flow_node WHERE business_id = t.business_id AND is_pass = 0 AND deleted = 0)
                               THEN (SELECT MIN(order_no) FROM t_biz_flow_node WHERE business_id = t.business_id AND is_pass = 0 AND deleted = 0)
                           -- 其次选择待处理的节点
                           WHEN EXISTS (SELECT 1 FROM t_biz_flow_node WHERE business_id = t.business_id AND is_pass IS NULL AND deleted = 0)
                               THEN (SELECT MIN(order_no) FROM t_biz_flow_node WHERE business_id = t.business_id AND is_pass IS NULL AND deleted = 0)
                           -- 如果既没有驳回也没有待处理，返回NULL
                           ELSE NULL
                       END AS min_order_no
                FROM (SELECT DISTINCT business_id FROM t_biz_flow_node WHERE deleted = 0 AND business_id IS NOT NULL) t
            ) t2
              ON t1.business_id = t2.business_id
             AND t1.order_no = t2.min_order_no
            WHERE t1.deleted = 0
              AND t1.business_id IS NOT NULL
        ) t
          ON m.id = t.business_id
        WHERE m.deleted = 0
        <if test="reqVO.acceptNo != null and reqVO.acceptNo != ''">
            AND m.accept_no LIKE CONCAT('%', #{reqVO.acceptNo}, '%')
        </if>
        <if test="reqVO.flow != null and reqVO.flow != ''">
            AND (t.order_no &gt;= #{reqVO.flow} or audit_status= 4)
        </if>
        <if test="reqVO.unitName != null and reqVO.unitName != ''">
            AND m.unit_name LIKE CONCAT('%', #{reqVO.unitName}, '%')
        </if>
        <if test="reqVO.unitId != null">
            AND m.unit_id = #{reqVO.unitId}
        </if>
        <if test="reqVO.materialType != null and reqVO.materialType != ''">
            AND m.material_type = #{reqVO.materialType}
        </if>
        <if test="reqVO.supplier != null and reqVO.supplier != ''">
            AND m.supplier LIKE CONCAT('%', #{reqVO.supplier}, '%')
        </if>
        <if test="reqVO.visualCheck != null and reqVO.visualCheck != ''">
            AND m.visual_check = #{reqVO.visualCheck}
        </if>
        <if test="reqVO.principleCheck != null and reqVO.principleCheck != ''">
            AND m.principle_check = #{reqVO.principleCheck}
        </if>
        <if test="reqVO.problemOpinion != null and reqVO.problemOpinion != ''">
            AND m.problem_opinion = #{reqVO.problemOpinion}
        </if>
        <if test="reqVO.usageDesc != null and reqVO.usageDesc != ''">
            AND m.usage_desc = #{reqVO.usageDesc}
        </if>
        <if test="reqVO.materialSituation != null and reqVO.materialSituation != ''">
            AND m.material_situation = #{reqVO.materialSituation}
        </if>
        <if test="reqVO.qualityInspection != null and reqVO.qualityInspection != ''">
            AND m.quality_inspection = #{reqVO.qualityInspection}
        </if>
        <if test="reqVO.acceptDate != null and reqVO.acceptDate.length == 2">
            AND m.accept_date BETWEEN #{reqVO.acceptDate[0]} AND #{reqVO.acceptDate[1]}
        </if>
        <if test="reqVO.acceptUser != null and reqVO.acceptUser != ''">
            AND m.accept_user = #{reqVO.acceptUser}
        </if>
        <if test="reqVO.signArea != null and reqVO.signArea != ''">
            AND m.sign_area = #{reqVO.signArea}
        </if>
        <if test="reqVO.fileIds != null and reqVO.fileIds != ''">
            AND m.file_ids = #{reqVO.fileIds}
        </if>
        <if test="reqVO.createTime != null and reqVO.createTime.length == 2">
            AND m.create_time BETWEEN #{reqVO.createTime[0]} AND #{reqVO.createTime[1]}
        </if>
        <if test="reqVO.materialBelong != null and reqVO.materialBelong != ''">
            AND m.material_belong = #{reqVO.materialBelong}
        </if>
        <if test="reqVO.inboundNo != null and reqVO.inboundNo != ''">
            AND m.inbound_no = #{reqVO.inboundNo}
        </if>
        <if test="reqVO.inboundType != null and reqVO.inboundType != ''">
            AND m.inbound_type = #{reqVO.inboundType}
        </if>
        <if test="reqVO.outboundType != null and reqVO.outboundType != ''">
            AND m.outbound_type = #{reqVO.outboundType}
        </if>
        <if test="reqVO.relatedNo != null and reqVO.relatedNo != ''">
            AND m.related_no = #{reqVO.relatedNo}
        </if>
        <if test="reqVO.warehouseId != null">
            AND m.warehouse_id = #{reqVO.warehouseId}
        </if>
        <if test="reqVO.warehouseName != null and reqVO.warehouseName != ''">
            AND m.warehouse_name LIKE CONCAT('%', #{reqVO.warehouseName}, '%')
        </if>
        <if test="reqVO.expectedInboundTime != null and reqVO.expectedInboundTime.length == 2">
            AND m.expected_inbound_time BETWEEN #{reqVO.expectedInboundTime[0]} AND #{reqVO.expectedInboundTime[1]}
        </if>
        <if test="reqVO.remark != null and reqVO.remark != ''">
            AND m.remark LIKE CONCAT('%', #{reqVO.remark}, '%')
        </if>
        <if test="reqVO.inboundOperator != null and reqVO.inboundOperator != ''">
            AND m.inbound_operator = #{reqVO.inboundOperator}
        </if>
        <if test="reqVO.type != null and reqVO.type != ''">
            AND m.type = #{reqVO.type}
        </if>
        <if test="reqVO.auditStatus != null">
            AND (
            <if test="reqVO.auditStatus == 1 or reqVO.auditStatus == 2">
                (
                (m.audit_status = 1 AND t.dept_id = #{loginUserDeptId} AND t.role_id IN
                <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
                )
                OR
                (m.audit_status = 1 AND NOT (t.dept_id = #{loginUserDeptId} AND t.role_id IN
                <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
                ))
                )
            </if>
            <if test="reqVO.auditStatus != 1 and reqVO.auditStatus != 2">
                m.audit_status = #{reqVO.auditStatus}
            </if>
            )
        </if>
        <if test="reqVO.inspectionStatus != null">
            AND m.inspection_status = #{reqVO.inspectionStatus}
        </if>
        <if test="reqVO.inspectionType != null and reqVO.inspectionType != ''">
            AND m.inspection_type = #{reqVO.inspectionType}
        </if>
        <if test="reqVO.materialClass != null and reqVO.materialClass != ''">
            AND m.material_class = #{reqVO.materialClass}
        </if>
        <if test="reqVO.spec != null and reqVO.spec != ''">
            AND m.spec LIKE CONCAT('%', #{reqVO.spec}, '%')
        </if>
        <if test="reqVO.unit != null and reqVO.unit != ''">
            AND m.unit = #{reqVO.unit}
        </if>
        <if test="reqVO.quantity != null">
            AND m.quantity = #{reqVO.quantity}
        </if>
        <if test="reqVO.originId != null">
            AND m.origin_id = #{reqVO.originId}
        </if>
        <if test="reqVO.dispatchOrderId != null">
            AND m.dispatch_order_id = #{reqVO.dispatchOrderId}
        </if>
        <if test="reqVO.dispatchOrderNo != null and reqVO.dispatchOrderNo != ''">
            AND m.dispatch_order_no = #{reqVO.dispatchOrderNo}
        </if>
        <if test="reqVO.receiveUnit != null and reqVO.receiveUnit != ''">
            AND m.receive_unit LIKE CONCAT('%', #{reqVO.receiveUnit}, '%')
        </if>
        <if test="reqVO.receiveId != null">
            AND m.receive_id = #{reqVO.receiveId}
        </if>
        <if test="reqVO.createDeptId != null">
            AND m.create_dept_id = #{reqVO.createDeptId}
        </if>
        <if test="reqVO.unitAddress != null and reqVO.unitAddress != ''">
            AND m.unit_address LIKE CONCAT('%', #{reqVO.unitAddress}, '%')
        </if>
        <if test="reqVO.contactPerson != null and reqVO.contactPerson != ''">
            AND m.contact_person LIKE CONCAT('%', #{reqVO.contactPerson}, '%')
        </if>
        <if test="reqVO.contactPhone != null and reqVO.contactPhone != ''">
            AND m.contact_phone LIKE CONCAT('%', #{reqVO.contactPhone}, '%')
        </if>
        <if test="reqVO.materialName != null and reqVO.materialName != ''">
            AND m.material_name LIKE CONCAT('%', #{reqVO.materialName}, '%')
        </if>
        <if test="reqVO.originalLocation != null and reqVO.originalLocation != ''">
            AND m.original_location LIKE CONCAT('%', #{reqVO.originalLocation}, '%')
        </if>
        <if test="reqVO.location != null and reqVO.location != ''">
            AND m.location LIKE CONCAT('%', #{reqVO.location}, '%')
        </if>
        <if test="reqVO.locationId != null">
            AND m.location_id = #{reqVO.locationId}
        </if>
        <if test="reqVO.newLocation != null and reqVO.newLocation != ''">
            AND m.new_location LIKE CONCAT('%', #{reqVO.newLocation}, '%')
        </if>
        <if test="reqVO.checkRange != null and reqVO.checkRange != ''">
            AND m.check_range = #{reqVO.checkRange}
        </if>
    </select>

    <!-- 已审核通过的物资信息查询结果映射 -->
    <resultMap id="MaterialAcceptApprovedResultMap" type="cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo.MaterialAcceptApprovedRespVO">
        <id property="id" column="id"/>
        <result property="acceptNo" column="accept_no"/>
        <result property="unitName" column="unit_name"/>
        <result property="type" column="type"/>
        <result property="approvedTime" column="approved_time"/>
        <result property="inspectionStatus" column="inspection_status"/>
        <result property="inspectionWarehouseIds" column="inspection_warehouse_ids"/>
    </resultMap>

    <!-- 查询已审核通过的物资信息 -->
    <select id="selectApprovedPage" resultMap="MaterialAcceptApprovedResultMap">
        SELECT a.id,
               a.accept_no,
               a.unit_name,
               a.type,
               a.inspection_status,
               a.inspection_warehouse_ids,
               a.inspection_type,
               b.update_time AS approved_time
          FROM (SELECT id,
                       accept_no,
                       unit_name,
                       type,
                       inspection_status,
                       inspection_warehouse_ids,
                       inspection_type
                  FROM t_material_accept
                 WHERE deleted = 0
                   AND audit_status = 4
                   <if test="reqVO.acceptNo != null and reqVO.acceptNo != ''">
                       AND accept_no LIKE CONCAT('%', #{reqVO.acceptNo}, '%')
                   </if>
                   <if test="reqVO.unitName != null and reqVO.unitName != ''">
                       AND unit_name LIKE CONCAT('%', #{reqVO.unitName}, '%')
                   </if>
                   <if test="reqVO.unitId != null">
                       AND unit_id = #{reqVO.unitId}
                   </if>
                   <if test="reqVO.type != null and reqVO.type != ''">
                       AND type = #{reqVO.type}
                   </if>
                    <if test="reqVO.createDeptId != null">
                        AND create_dept_id = #{reqVO.createDeptId}
                    </if>
                   <if test="reqVO.createDeptIdList != null and reqVO.createDeptIdList.size() > 0">
                       AND create_dept_id IN
                       <foreach item="item" collection="reqVO.createDeptIdList" separator="," close=")" open="(" index="">
                           #{item}
                       </foreach>
                   </if>

                ) a
     LEFT JOIN (SELECT business_id,
                       MAX(update_time) AS update_time
                  FROM t_biz_flow_node
                 WHERE deleted = 0
                   AND business_id IS NOT NULL
                   AND is_pass = 1
              GROUP BY business_id) b
            ON a.id = b.business_id
         WHERE 1=1
         <if test="reqVO.approvedTime != null and reqVO.approvedTime.length == 2">
             AND b.update_time BETWEEN #{reqVO.approvedTime[0]} AND #{reqVO.approvedTime[1]}
         </if>
         ORDER BY b.update_time DESC
         <if test="reqVO.pageSize != null">
            <if test="reqVO.offset != null">
                OFFSET #{reqVO.offset} ROWS FETCH NEXT #{reqVO.pageSize} ROWS ONLY
            </if>
        </if>
    </select>

    <!-- 计算已审核通过的物资信息总数 -->
    <select id="selectApprovedCount" resultType="java.lang.Long">
        SELECT COUNT(1)
            FROM (SELECT id,
            accept_no,
            unit_name,
            type,
            inspection_status,
            inspection_warehouse_ids,
            inspection_type
            FROM t_material_accept
            WHERE deleted = 0
            AND audit_status = 4
            <if test="reqVO.acceptNo != null and reqVO.acceptNo != ''">
                AND accept_no LIKE CONCAT('%', #{reqVO.acceptNo}, '%')
            </if>
            <if test="reqVO.unitName != null and reqVO.unitName != ''">
                AND unit_name LIKE CONCAT('%', #{reqVO.unitName}, '%')
            </if>
            <if test="reqVO.unitId != null">
                AND unit_id = #{reqVO.unitId}
            </if>
            <if test="reqVO.type != null and reqVO.type != ''">
                AND type = #{reqVO.type}
            </if>
            <if test="reqVO.createDeptId != null">
                AND create_dept_id = #{reqVO.createDeptId}
            </if>
            <if test="reqVO.createDeptIdList != null and reqVO.createDeptIdList.size() > 0">
                AND create_dept_id IN
                <foreach item="item" collection="reqVO.createDeptIdList" separator="," close=")" open="(" index="">
                    #{item}
                </foreach>
            </if>
            ) a
            LEFT JOIN (SELECT business_id,
            MAX(update_time) AS update_time
            FROM t_biz_flow_node
            WHERE deleted = 0
            AND business_id IS NOT NULL
            AND is_pass = 1
            GROUP BY business_id) b
            ON a.id = b.business_id
            WHERE 1=1
            <if test="reqVO.approvedTime != null and reqVO.approvedTime.length == 2">
                AND b.update_time BETWEEN #{reqVO.approvedTime[0]} AND #{reqVO.approvedTime[1]}
            </if>
    </select>

    <!-- 预警处置数据统计结果映射 -->
    <resultMap id="MaterialAcceptWarningStatsResultMap" type="cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo.MaterialAcceptWarningStatsRespVO">
        <result property="totalCount" column="totalCount"/>
        <result property="pendingCount" column="pendingCount"/>
        <result property="processedCount" column="processedCount"/>
        <result property="processingRate" column="processingRate"/>
    </resultMap>

    <!-- 统计预警处置数据（总数、待处理数、已处理数、处理率） -->
    <select id="getWarningStatsByDeptId" resultMap="MaterialAcceptWarningStatsResultMap">
        SELECT
            COUNT(*) as totalCount,
            COUNT(CASE WHEN audit_status != 4 THEN 1 END) as pendingCount,
            COUNT(CASE WHEN audit_status = 4 THEN 1 END) as processedCount,
            CASE
                WHEN COUNT(*) = 0 THEN '0%'
                ELSE CONCAT(ROUND(COUNT(CASE WHEN audit_status = 4 THEN 1 END) * 100.0 / COUNT(*), 1), '%')
            END as processingRate
        FROM t_material_accept
        WHERE deleted = 0
          AND type = '预警处置'
          AND create_dept_id = #{deptId}
    </select>

    <!-- 仓库统计信息查询结果映射 -->
    <resultMap id="WarehouseStatsResultMap" type="cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo.WarehouseStatsRespVO">
        <result property="id" column="id"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="totalArea" column="totalArea"/>
        <result property="useArea" column="useArea"/>
        <result property="unusedArea" column="unusedArea"/>
        <result property="usageRate" column="usageRate"/>
        <result property="utilizationLevel" column="utilizationLevel"/>
        <result property="spaceStatus" column="spaceStatus"/>
    </resultMap>

    <!-- 查询仓库统计信息 -->
    <select id="selectWarehouseStats" resultMap="WarehouseStatsResultMap">
        SELECT
            w.id,
            w.warehouse_name,
            w.area AS totalArea,
            COALESCE(SUM(ROUND((sa.width + 60) * (sa.height + 60) / 3600)), 0) AS useArea,
            (w.area - COALESCE(SUM(ROUND((sa.width + 60) * (sa.height + 60) / 3600)), 0)) AS unusedArea,
            CASE
                WHEN w.area > 0 THEN
                    ROUND(COALESCE(SUM(ROUND((sa.width + 60) * (sa.height + 60) / 3600)), 0) * 100.0 / w.area, 2)
                ELSE 0
            END AS usageRate,
            CASE
                WHEN w.area > 0 THEN
                    CASE
                        WHEN ROUND(COALESCE(SUM(ROUND((sa.width + 60) * (sa.height + 60) / 3600)), 0) * 100.0 / w.area, 2) >= 90 THEN '高利用率'
                        WHEN ROUND(COALESCE(SUM(ROUND((sa.width + 60) * (sa.height + 60) / 3600)), 0) * 100.0 / w.area, 2) >= 70 THEN '中等利用率'
                        WHEN ROUND(COALESCE(SUM(ROUND((sa.width + 60) * (sa.height + 60) / 3600)), 0) * 100.0 / w.area, 2) >= 50 THEN '低利用率'
                        ELSE '极低利用率'
                    END
                ELSE '无数据'
            END AS utilizationLevel,
            CASE
                WHEN (w.area - COALESCE(SUM(ROUND((sa.width + 60) * (sa.height + 60) / 3600)), 0)) > 100 THEN '有较大剩余空间'
                WHEN (w.area - COALESCE(SUM(ROUND((sa.width + 60) * (sa.height + 60) / 3600)), 0)) > 50 THEN '有少量剩余空间'
                ELSE '空间紧张'
            END AS spaceStatus
        FROM T_WAREHOUSE w
            LEFT JOIN T_WAREHOUSE_PLANS wp ON w.id = wp.ck_id AND wp.deleted = 0
            LEFT JOIN T_STACK_AREAS sa ON wp.id = sa.plan_id AND sa.deleted = 0
        WHERE w.deleted = 0
          AND w.id = #{warehouseId}
        GROUP BY w.id, w.warehouse_name, w.area
    </select>

    <select id="getInventory" resultMap="MaterialAcceptWithFlowResultMap">
        SELECT
            m.id,
            m.accept_no,
            m.unit_name,
            m.unit_id,
            m.material_type,
            m.supplier,
            m.visual_check,
            m.principle_check,
            m.problem_opinion,
            m.usage_desc,
            m.material_situation,
            m.quality_inspection,
            m.accept_date,
            m.accept_user,
            m.file_ids,
            m.create_time,
            m.material_belong,
            m.inbound_no,
            m.inbound_type,
            m.outbound_type,
            m.related_no,
            m.warehouse_id,
            m.warehouse_name,
            m.expected_inbound_time,
            m.remark,
            m.inbound_operator,
            m.flow,
            m.create_dept_id,
            m.type,
            m.creator,
            m.updater,
            m.update_time,
            m.deleted,
            m.audit_status,
            m.inspection_status,
            m.material_class,
            m.spec,
            m.unit,
            m.quantity,
            m.origin_id,
            m.dispatch_order_id,
            m.dispatch_order_no,
            m.receive_unit,
            m.receive_id,
            m.unit_address,
            m.contact_person,
            m.contact_phone,
            m.material_name,
            m.original_location,
            m.location,
            m.location_id,
            m.new_location,
            m.plan_time,
            m.check_range,
            m.pro_factory_name,
            m.pro_factory_id,
            m.types,
            m.inspection_warehouse_ids,
            m.inspection_type,
            m.material_name,
            m.material_code,
            m.original_location,
            m.location,
            m.location_id,
            m.new_location,
            m.plan_time,
            m.check_range
        FROM t_material_accept m
        where 1=1
          and (m.type like '%出库%' or m.type like '%入库%')
          and m.third_result_flag = 1
        <if test="list != null and list.size() > 0">
           and create_dept_id in
            <foreach item="item" collection="list" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
        order by m.create_time desc
    </select>
</mapper>
