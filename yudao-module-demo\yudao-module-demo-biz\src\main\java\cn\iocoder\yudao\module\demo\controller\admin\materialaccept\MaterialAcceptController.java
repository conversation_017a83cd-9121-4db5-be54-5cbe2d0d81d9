package cn.iocoder.yudao.module.demo.controller.admin.materialaccept;

import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.demo.controller.admin.bizflownode.vo.BizFlowNodeRespVO;
import cn.iocoder.yudao.module.demo.controller.admin.materialaccept.enums.MeterialBelongType;
import cn.iocoder.yudao.module.demo.controller.admin.materialaccept.enums.MeterialTypeEnum;
import cn.iocoder.yudao.module.demo.controller.admin.materialaccept.enums.YhczLevelEnum;
import cn.iocoder.yudao.module.demo.controller.admin.materialaccept.enums.YhczMeterialTypeEnum;
import cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo.export.*;
import cn.iocoder.yudao.module.demo.controller.admin.materialacceptdetail.vo.MaterialAcceptDetailRespVO;
import cn.iocoder.yudao.module.demo.controller.admin.materialacceptdetail.vo.MaterialAcceptDetailSaveReqVO;
import cn.iocoder.yudao.module.demo.controller.admin.checkstandard.vo.MaterialCheckStandardRespVO;
import cn.iocoder.yudao.module.demo.controller.admin.checkstandard.vo.MaterialCheckStandardSaveReqVO;
import cn.iocoder.yudao.module.demo.dal.dataobject.bizflownode.BizFlowNodeDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialacceptdetail.MaterialAcceptDetailDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.checkstandard.MaterialCheckStandardDO;
import cn.iocoder.yudao.module.demo.openapi.service.MaterialAcceptReturnService;
import cn.iocoder.yudao.module.demo.openapi.vo.MaterialAcceptReturnVO;
import cn.iocoder.yudao.module.demo.service.bizflownode.BizFlowNodeService;
import cn.iocoder.yudao.module.demo.service.checkstandard.MaterialCheckStandardService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.util.ArrayList;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo.*;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialaccept.MaterialAcceptDO;
import cn.iocoder.yudao.module.demo.service.materialaccept.MaterialAcceptService;

/**
 * 管理后台 - 物资验收信息 Controller
 *
 * 支持物资验收信息的增删改查，包括验收/入库/出库等类型处理
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "管理后台 - 物资验收信息")
@RestController
@RequestMapping("/demo/material-accept")
@Validated
public class MaterialAcceptController {

    @Resource
    private MaterialAcceptService materialAcceptService;

    @Resource
    private BizFlowNodeService bizFlowNodeService;

    @Resource
    private MaterialCheckStandardService materialCheckStandardService;

    @Resource
    private MaterialAcceptReturnService materialAcceptReturnService;

    @PostMapping("/create")
    @Operation(summary = "创建物资验收信息", description = "创建物资验收信息，可包含调令信息、物资名称、原货位、目标货位等信息")
    @PreAuthorize("@ss.hasPermission('demo:material-accept:create')")
    public CommonResult<Long> createMaterialAccept(@Valid @RequestBody MaterialAcceptSaveReqVO createReqVO) {
        createReqVO.setDataSource("PC端");
        // 创建物资验收信息及其详情
        Long acceptId = materialAcceptService.createMaterialAccept(createReqVO);

        // 如果有检查标准信息，保存检查标准
        if (createReqVO.getCheckStandards() != null && !createReqVO.getCheckStandards().isEmpty()) {
            // 设置关联的验收单ID
            createReqVO.getCheckStandards().forEach(standard -> standard.setAcceptId(acceptId));
            // 批量保存检查标准
            materialCheckStandardService.createMaterialCheckStandards(createReqVO.getCheckStandards());
        }

        return success(acceptId);
    }

    @PostMapping("/createByOther")
    @Operation(summary = "创建物资验收信息", description = "创建物资验收信息，可包含调令信息、物资名称、原货位、目标货位等信息")
//    @PreAuthorize("@ss.hasPermission('demo:material-accept:create')")
    public CommonResult<Long> createByOtherMaterialAccept(@Valid @RequestBody MaterialAcceptSaveReqVO createReqVO) {
        createReqVO.setDataSource("PDA端");
        createReqVO.setFlow(1);
        // 创建物资验收信息及其详情
        Long acceptId = materialAcceptService.createMaterialAccept(createReqVO);

        // 如果有检查标准信息，保存检查标准
        if (createReqVO.getCheckStandards() != null && !createReqVO.getCheckStandards().isEmpty()) {
            // 设置关联的验收单ID
            createReqVO.getCheckStandards().forEach(standard -> standard.setAcceptId(acceptId));
            // 批量保存检查标准
            materialCheckStandardService.createMaterialCheckStandards(createReqVO.getCheckStandards());
        }

        return success(acceptId);
    }

    @PutMapping("/update")
    @Operation(summary = "更新物资验收信息", description = "更新物资验收信息，包括物资名称、原货位、目标货位等")
    @PreAuthorize("@ss.hasPermission('demo:material-accept:update')")
    public CommonResult<Boolean> updateMaterialAccept(@Valid @RequestBody MaterialAcceptSaveReqVO updateReqVO) {
        // 更新物资验收信息及其详情
        materialAcceptService.updateMaterialAccept(updateReqVO);

        // 处理检查标准信息
        if (updateReqVO.getId() != null) { // 确保有ID才进行处理
            // 先删除原有的检查标准
            materialCheckStandardService.deleteMaterialCheckStandardsByAcceptId(updateReqVO.getId());

            // 如果有新的检查标准，则保存
            if (updateReqVO.getCheckStandards() != null && !updateReqVO.getCheckStandards().isEmpty()) {
                // 设置关联的验收单ID
                updateReqVO.getCheckStandards().forEach(standard -> standard.setAcceptId(updateReqVO.getId()));
                // 批量保存检查标准
                materialCheckStandardService.createMaterialCheckStandards(updateReqVO.getCheckStandards());
            }
        }

        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除物资验收信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('demo:material-accept:delete')")
    public CommonResult<Boolean> deleteMaterialAccept(@RequestParam("id") Long id) {
        // 删除物资验收信息
        materialAcceptService.deleteMaterialAccept(id);

        // 删除关联的检查标准
        materialCheckStandardService.deleteMaterialCheckStandardsByAcceptId(id);

        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得物资验收信息", description = "获取物资验收信息详情，包含验收单信息、物资详情、调令信息、出入库类型等")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('demo:material-accept:query')")
    public CommonResult<MaterialAcceptRespVO> getMaterialAccept(@RequestParam("id") Long id) {
        // 获取主表信息
        MaterialAcceptDO materialAccept = materialAcceptService.getMaterialAccept(id);
        if (materialAccept == null) {
            return success(null); // 或者返回错误码
        }

        // 获取关联的详情列表
        List<MaterialAcceptDetailDO> details = materialAcceptService.getMaterialAcceptDetailsByAcceptId(id);

        // 获取关联的检查标准列表
        List<MaterialCheckStandardDO> checkStandards = materialCheckStandardService.getMaterialCheckStandardsByAcceptId(id);

        // 将 DO 转为 RespVO 并设置详情列表
        MaterialAcceptRespVO respVO = BeanUtils.toBean(materialAccept, MaterialAcceptRespVO.class);
        respVO.setDetails(BeanUtils.toBean(details, MaterialAcceptDetailRespVO.class));
        respVO.setCheckStandards(BeanUtils.toBean(checkStandards, MaterialCheckStandardRespVO.class));

        String type = materialAccept.getType();
        if (type.contains("出库")){
            type = "物资出库";
        }
        // 获取业务流程节点列表
        List<BizFlowNodeDO> flowNodes = bizFlowNodeService.getBizFlowNodeListByCreateDeptAndBizType(
                materialAccept.getCreateDeptId(),
                type
        );
        // 查询与该验收单关联的业务流程节点（通过businessId）
        List<BizFlowNodeDO> relatedFlowNodes = bizFlowNodeService.getBizFlowNodeListByBusinessId(
                materialAccept.getId()
        );

        // 如果有关联的流程节点，则使用关联的；否则使用部门和业务类型查询的通用流程
        if (!relatedFlowNodes.isEmpty()) {
            respVO.setFlowNodes(BeanUtils.toBean(relatedFlowNodes, BizFlowNodeRespVO.class));
        } else {
            respVO.setFlowNodes(BeanUtils.toBean(flowNodes, BizFlowNodeRespVO.class));
        }

        if (StringUtils.isNotBlank(respVO.getFileList())) {
            String fileList = respVO.getFileList();

            JSONArray jsonArray = JSON.parseArray(fileList);
            JSONObject jsonObject = jsonArray.getJSONObject(0);
            String fileUrl = (String) jsonObject.get("fileUrl");

            respVO.setPdaFileUrl(fileUrl);
        }

        return success(respVO);
    }

    @PostMapping("/copy")
    @Operation(summary = "复制物资验收信息", description = "根据ID查询物资信息及关联的详情，并复制出一条新数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('demo:material-accept:create')")
    public CommonResult<Long> copyMaterialAccept(@RequestParam("id") Long id) {
        // 获取主表信息
        MaterialAcceptDO materialAccept = materialAcceptService.getMaterialAccept(id);
        if (materialAccept == null) {
            return success(null); // 或者返回错误码
        }

        // 获取关联的详情列表
        List<MaterialAcceptDetailDO> details = materialAcceptService.getMaterialAcceptDetailsByAcceptId(id);

        // 获取关联的检查标准列表
        List<MaterialCheckStandardDO> checkStandards = materialCheckStandardService.getMaterialCheckStandardsByAcceptId(id);

        // 转换为保存请求VO
        MaterialAcceptSaveReqVO createReqVO = BeanUtils.toBean(materialAccept, MaterialAcceptSaveReqVO.class);
        // 清除ID，让系统生成新的ID
        createReqVO.setId(null);
        // 清除验收单号，让系统根据type重新生成
        createReqVO.setAcceptNo(null);
        // 设置流程状态为未提交(0)
        createReqVO.setFlow(0);
        // 转换详情列表
        if (details != null && !details.isEmpty()) {
            List<MaterialAcceptDetailSaveReqVO> detailSaveReqVOs = new ArrayList<>();
            for (MaterialAcceptDetailDO detail : details) {
                MaterialAcceptDetailSaveReqVO detailVO = BeanUtils.toBean(detail, MaterialAcceptDetailSaveReqVO.class);
                detailVO.setId(null); // 清除ID，让系统生成新的ID
                detailSaveReqVOs.add(detailVO);
            }
            createReqVO.setDetails(detailSaveReqVOs);
        }

        // 创建新的物资验收信息
        Long newAcceptId = materialAcceptService.createMaterialAccept(createReqVO);
        // 转换检查标准列表
        if (checkStandards != null && !checkStandards.isEmpty()) {
            List<MaterialCheckStandardSaveReqVO> standardSaveReqVOs = new ArrayList<>();
            for (MaterialCheckStandardDO standard : checkStandards) {
                MaterialCheckStandardSaveReqVO standardVO = BeanUtils.toBean(standard, MaterialCheckStandardSaveReqVO.class);
                standardVO.setId(null); // 清除ID，让系统生成新的ID
                standardVO.setAcceptId(newAcceptId);
                standardVO.setCheckResult( null);
                materialCheckStandardService.createMaterialCheckStandard(standardVO);
            }

        }


        return success(newAcceptId);
    }

    @GetMapping("/page")
    @Operation(summary = "获得物资验收信息分页", description = "支持按验收单号、物资名称、原货位、目标货位、调令信息等条件查询")
    @PreAuthorize("@ss.hasPermission('demo:material-accept:query')")
    public CommonResult<PageResult<MaterialAcceptWithFlowRespVO>> getMaterialAcceptPage(@Valid MaterialAcceptPageReqVO pageReqVO) {
        // 使用关联查询
        PageResult<MaterialAcceptWithFlowRespVO> pageResult = materialAcceptService.getMaterialAcceptWithFlowPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出物资验收信息 Excel", description = "支持导出包含物资名称、原货位、目标货位等完整数据")
    @PreAuthorize("@ss.hasPermission('demo:material-accept:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMaterialAcceptExcel(@Valid MaterialAcceptPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<MaterialAcceptWithFlowRespVO> pageResult = materialAcceptService.getMaterialAcceptWithFlowPage(pageReqVO);

        exportBusinessExcel(pageReqVO.getType(), response, pageResult.getList());

    }

    @GetMapping("/receive-report")
    @PermitAll
    @Operation(summary = "导出物资验收单", description = "支持导出包含物资名称、原货位、目标货位等完整数据")
    @ApiAccessLog(operateType = EXPORT)
    public void receiveReport(HttpServletResponse response) throws IOException {
        materialAcceptService.downloadReceiveReport(response);
    }

    private void exportBusinessExcel(String type, HttpServletResponse response, List<MaterialAcceptWithFlowRespVO> list)
            throws IOException {
        if (CollectionUtils.isNotEmpty(list)) {
            for (MaterialAcceptWithFlowRespVO one : list) {
                one.setMaterialBelong(MeterialBelongType.getDescByCode(one.getMaterialBelong()));

                if (!"隐患处置".equals(type)) {
                    one.setMaterialType(MeterialTypeEnum.getDescByCode(one.getMaterialType()));
                } else {
                    one.setMaterialType(YhczMeterialTypeEnum.getDescByCode(one.getMaterialType()));
                }

                one.setCheckRange(YhczLevelEnum.getDescByCode(one.getCheckRange()));
            }
        }
        if ("物资验收".equals(type)) {
            List<WzysExportVO> targetList = convertWzysExportVOList(list);

            ExcelUtils.write(response, "物资验收信息.xls", "数据", WzysExportVO.class,
                    targetList);
        } else if ("物资入库".equals(type)) {
            List<WzrkExportVO> targetList = convertWzrkExportVOList(list);

            ExcelUtils.write(response, "物资入库信息.xls", "数据", WzrkExportVO.class,
                    targetList);
        } else if ("调运/前置出库".equals(type)) {
            List<DyQzckExportVO> targetList = convertDyQzckExportVOList(list);

            ExcelUtils.write(response, "调运/前置出库信息.xls", "数据", DyQzckExportVO.class,
                    targetList);
        } else if ("其他出库".equals(type)) {
            List<QtckExportVO> targetList = convertQtckExportVOList(list);

            ExcelUtils.write(response, "其他出库信息.xls", "数据", QtckExportVO.class,
                    targetList);
        } else if ("物资盘点".equals(type)) {
            List<WzpdExportVO> targetList = convertWzpdExportVOList(list);

            ExcelUtils.write(response, "物资盘点信息.xls", "数据", WzpdExportVO.class,
                    targetList);
        } else if ("巡检盘点".equals(type)) {
            List<XjpdExportVO> targetList = convertXjpdExportVOList(list);

            ExcelUtils.write(response, "巡检盘点信息.xls", "数据", XjpdExportVO.class,
                    targetList);
        } else if ("维修保养".equals(type)) {
            List<WxbyExportVO> targetList = convertWxbyExportVOList(list);

            ExcelUtils.write(response, "维修保养信息.xls", "数据", WxbyExportVO.class,
                    targetList);
        } else if ("报废处置".equals(type)) {
            List<BfczExportVO> targetList = convertBfczExportVOList(list);

            ExcelUtils.write(response, "报废处置信息.xls", "数据", BfczExportVO.class,
                    targetList);
        } else if ("预案管理".equals(type)) {
            List<YaglExportVO> targetList = convertYaglExportVOList(list);

            ExcelUtils.write(response, "预案管理信息.xls", "数据", YaglExportVO.class,
                    targetList);
        } else if ("隐患处置".equals(type)) {
            List<YhczExportVO> targetList = convertYhczExportVOList(list);

            ExcelUtils.write(response, "隐患处置信息.xls", "数据", YhczExportVO.class,
                    targetList);
        }
    }

    private List<YhczExportVO> convertYhczExportVOList(List<MaterialAcceptWithFlowRespVO> list) {
        List<YhczExportVO> targetList = Lists.newArrayList();

        for (MaterialAcceptWithFlowRespVO one : list) {
            YhczExportVO target = new YhczExportVO();

            org.springframework.beans.BeanUtils.copyProperties(one, target);

            targetList.add(target);
        }

        return targetList;
    }

    private List<YaglExportVO> convertYaglExportVOList(List<MaterialAcceptWithFlowRespVO> list) {
        List<YaglExportVO> targetList = Lists.newArrayList();

        for (MaterialAcceptWithFlowRespVO one : list) {
            YaglExportVO target = new YaglExportVO();

            org.springframework.beans.BeanUtils.copyProperties(one, target);

            targetList.add(target);
        }

        return targetList;
    }

    private List<BfczExportVO> convertBfczExportVOList(List<MaterialAcceptWithFlowRespVO> list) {
        List<BfczExportVO> targetList = Lists.newArrayList();

        for (MaterialAcceptWithFlowRespVO one : list) {
            BfczExportVO target = new BfczExportVO();

            org.springframework.beans.BeanUtils.copyProperties(one, target);

            targetList.add(target);
        }

        return targetList;
    }

    private List<WxbyExportVO> convertWxbyExportVOList(List<MaterialAcceptWithFlowRespVO> list) {
        List<WxbyExportVO> targetList = Lists.newArrayList();

        for (MaterialAcceptWithFlowRespVO one : list) {
            WxbyExportVO target = new WxbyExportVO();

            org.springframework.beans.BeanUtils.copyProperties(one, target);

            targetList.add(target);
        }

        return targetList;
    }

    private List<XjpdExportVO> convertXjpdExportVOList(List<MaterialAcceptWithFlowRespVO> list) {
        List<XjpdExportVO> targetList = Lists.newArrayList();

        for (MaterialAcceptWithFlowRespVO one : list) {
            XjpdExportVO target = new XjpdExportVO();

            org.springframework.beans.BeanUtils.copyProperties(one, target);

            targetList.add(target);
        }

        return targetList;
    }

    private List<WzpdExportVO> convertWzpdExportVOList(List<MaterialAcceptWithFlowRespVO> list) {
        List<WzpdExportVO> targetList = Lists.newArrayList();

        for (MaterialAcceptWithFlowRespVO one : list) {
            WzpdExportVO target = new WzpdExportVO();

            org.springframework.beans.BeanUtils.copyProperties(one, target);

            targetList.add(target);
        }

        return targetList;
    }

    private List<QtckExportVO> convertQtckExportVOList(List<MaterialAcceptWithFlowRespVO> list) {
        List<QtckExportVO> targetList = Lists.newArrayList();

        for (MaterialAcceptWithFlowRespVO one : list) {
            QtckExportVO target = new QtckExportVO();

            org.springframework.beans.BeanUtils.copyProperties(one, target);

            targetList.add(target);
        }

        return targetList;
    }

    private List<DyQzckExportVO> convertDyQzckExportVOList(List<MaterialAcceptWithFlowRespVO> list) {
        List<DyQzckExportVO> targetList = Lists.newArrayList();

        for (MaterialAcceptWithFlowRespVO one : list) {
            DyQzckExportVO target = new DyQzckExportVO();

            org.springframework.beans.BeanUtils.copyProperties(one, target);

            targetList.add(target);
        }

        return targetList;
    }

    private List<WzrkExportVO> convertWzrkExportVOList(List<MaterialAcceptWithFlowRespVO> list) {
        List<WzrkExportVO> targetList = Lists.newArrayList();

        for (MaterialAcceptWithFlowRespVO one : list) {
            WzrkExportVO target = new WzrkExportVO();

            org.springframework.beans.BeanUtils.copyProperties(one, target);

            targetList.add(target);
        }

        return targetList;
    }

    private List<WzysExportVO> convertWzysExportVOList(List<MaterialAcceptWithFlowRespVO> list) {
        List<WzysExportVO> targetList = Lists.newArrayList();

        for (MaterialAcceptWithFlowRespVO one : list) {
            WzysExportVO target = new WzysExportVO();

            org.springframework.beans.BeanUtils.copyProperties(one, target);

            targetList.add(target);
        }

        return targetList;
    }

    @GetMapping("/approved-page")
    @Operation(summary = "获得已审核通过的物资信息分页", description = "查询审核状态为已通过的物资信息，包含审核通过时间")
    @PreAuthorize("@ss.hasPermission('demo:material-accept:query')")
    public CommonResult<PageResult<MaterialAcceptApprovedRespVO>> getApprovedMaterialAcceptPage(@Valid MaterialAcceptApprovedPageReqVO pageReqVO) {
        // 使用关联查询
        PageResult<MaterialAcceptApprovedRespVO> pageResult = materialAcceptService.getApprovedMaterialAcceptPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-approved-excel")
    @Operation(summary = "导出已审核通过的物资信息 Excel", description = "导出审核状态为已通过的物资信息，包含审核通过时间")
    @PreAuthorize("@ss.hasPermission('demo:material-accept:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportApprovedMaterialAcceptExcel(@Valid MaterialAcceptApprovedPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10000);
        PageResult<MaterialAcceptApprovedRespVO> pageResult = materialAcceptService.getApprovedMaterialAcceptPage(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "作业单据.xls", "数据", MaterialAcceptApprovedRespVO.class,
                        pageResult.getList());
    }

    @GetMapping("/warning-stats")
    @Operation(summary = "获取预警处置数据统计", description = "统计指定部门的预警处置总数、待处理数、已处理数")
    public CommonResult<MaterialAcceptWarningStatsRespVO> getWarningStats() {
        // 获取当前登录用户的部门ID
        Long currentDeptId = SecurityFrameworkUtils.getLoginUserDeptId();
        // 如果当前用户没有部门ID，则返回空结果
        if (currentDeptId == null) {
            return success(null);
        }
        // 如果当前用户有部门ID，则使用该部门ID进行统计
        MaterialAcceptWarningStatsRespVO statsResult = materialAcceptService.getWarningStats(currentDeptId);
        return success(statsResult);
    }

    @GetMapping("/warehouse-stats")
    @Operation(summary = "根据厂库ID获取仓库的面积使用统计信息", description = "根据厂库ID获取仓库的面积使用统计信息")
    @PreAuthorize("@ss.hasPermission('demo:material-accept:query')")
    public CommonResult<WarehouseStatsRespVO> getWarehouseStats(@RequestParam("warehouseId") Long warehouseId) {
        WarehouseStatsRespVO statsResult = materialAcceptService.getWarehouseStats(warehouseId);
        return success(statsResult);
    }

    /**
     * 接收回传数据
     * @param updateReqVO
     * @return
     */
    @PostMapping("/accept")
    @PermitAll
    @Operation(summary = "接收回传数据")
    public CommonResult<Boolean> accept(@Valid @RequestBody MaterialAcceptReturnVO updateReqVO) {
        log.info("接收回传数据:{}", JSON.toJSONString(updateReqVO));
        materialAcceptReturnService.accept(updateReqVO);
        return success(true);
    }

}
