package cn.iocoder.yudao.module.demo.dal.mysql.materialaccept;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo.*;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialaccept.MaterialAcceptDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialaccept.MaterialReqDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * 物资Req Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MaterialReqMapper extends BaseMapperX<MaterialReqDO> {

}
