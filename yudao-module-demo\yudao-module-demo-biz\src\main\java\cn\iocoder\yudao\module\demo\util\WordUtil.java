package cn.iocoder.yudao.module.demo.util;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: sm
 */
@Slf4j
@Component
public class WordUtil {

    //输出word文件路径目录
    @Value("${fht.word.basePackagePath:/templates/word/}")
    @Getter
    @Setter
    private String basePackagePath = "/templates/word/";

    /**
     * 创建通过模板生成的文件
     *
     * @param dataModel
     * @param templateName
     * @param originalFileName
     * @return
     */
    public String createOss(Map<String, Object> dataModel, String templateName, String originalFileName) {
        BufferedWriter bufWrite = null;
        ByteArrayOutputStream byteOs = null;
        byte[] buffer = null;

        if(templateName.endsWith("docx")) {
            // 使用docx模版，支持文件预览服务，ftl的是xml格式不支持预览
            buffer = formatDocx(templateName, dataModel);
        }

        // 保存buffer到本地文件
        if (buffer != null) {
            try {
                // 确保目录存在
                File tmpDir = new File("D:\\sm-review\\tmp");
                if (!tmpDir.exists()) {
                    tmpDir.mkdirs();
                }

                // 生成文件名（使用时间戳避免重名）
                String timestamp = String.valueOf(System.currentTimeMillis());
                String localFileName = timestamp + "_" + originalFileName;
                File localFile = new File(tmpDir, localFileName);

                // 写入文件
                try (FileOutputStream fos = new FileOutputStream(localFile)) {
                    fos.write(buffer);
                    fos.flush();
                }
                log.info("文件已保存到: {}", localFile.getAbsolutePath());
            } catch (IOException e) {
                log.error("保存文件到本地失败", e);
            }
        }

        return null;
    }

    /**
     * 根据地址获得数据的字节流
     *
     * @param strUrl 网络连接地址
     * @return 图片Base64码
     */
    public String getImgBase64ByUrl(String strUrl) {

        try {

            // 建立 Http 链接
            HttpURLConnection conn = (HttpURLConnection) new URL(strUrl).openConnection();

            // 5秒响应超时
            conn.setConnectTimeout(5 * 1000);
            conn.setDoInput(true);

            // 判断http请求是否正常响应请求数据，如果正常获取图片 Base64 码
            if (conn.getResponseCode() == HttpURLConnection.HTTP_OK) {

                // 获取图片输入流
                InputStream inStream = conn.getInputStream();

                // 用于存储图片输出流
                ByteArrayOutputStream outStream = new ByteArrayOutputStream();

                // 定义缓存流，用于存储图片输出流
                byte[] buffer = new byte[1024];

                int len = 0;

                // 图片输出流循环写入
                while ((len = inStream.read(buffer)) != -1) {

                    outStream.write(buffer, 0, len);
                }

                // 图片输出流转字节流
                byte[] btImg = outStream.toByteArray();

                inStream.close();
                outStream.flush();
                outStream.close();

                Base64.Encoder encoder = Base64.getEncoder();
                return encoder.encodeToString(btImg);
            }
        } catch (Exception e) {
            log.error("生成图片Base64编码失败", e);
            throw new RuntimeException("生成图片Base64编码失败");
        }

        return null;
    }


    /**
     * @param templateFile
     * @param dataModel
     * @return
     */
    public byte[] formatDocx(String templateFile, Map<String, Object> dataModel) {
        String path = "templates/word/" + templateFile;
        // 加载模版文档对象
        XWPFDocument document = null;
        try {
            document = new XWPFDocument(OPCPackage.open(getClass().getClassLoader().getResourceAsStream(path)));
        } catch (IOException | InvalidFormatException e) {
            log.warn("生成报告文件异常", e);
            return null;
        }
        // 查根据占位符填充
        for (XWPFParagraph p : document.getParagraphs()) {
            StringBuilder sb = new StringBuilder();
            for (XWPFRun r : p.getRuns()) {
                String text = r.getText(0);
                sb.append(text);
            }

            String s = sb.toString();
            if (StringUtils.isNotBlank(s)) {
                s = formatTemplate(s, dataModel);
            }

            for (int i = 0; i < p.getRuns().size(); i++) {
                XWPFRun xwpfRun = p.getRuns().get(i);
                if (i == 0) {
                    String[] split = s.split("<br>");

                    for (int h = 0; h < split.length; h++) {
                        if (h != 0) {
                            xwpfRun.addBreak();
                        }

                        String splitStr = split[h];

                        xwpfRun.setText(splitStr, h);
                    }

                } else {
                    xwpfRun.setText("", 0);
                }
            }
        }
        try {
            ByteArrayOutputStream byteOs = new ByteArrayOutputStream();
            document.write(byteOs);
            return byteOs.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 利用占位符填充模版
     *
     * @param template
     * @param params
     * @return
     */
    public String formatTemplate(String template, Map<String, Object> params) {
        String reg = "(?<=\\$\\{)(.*?)(?=\\})";
        Pattern p = Pattern.compile(reg);
        Matcher m = p.matcher(template);
        String var = "";

        while (m.find()) {
            String segment = m.group();
            String defaultValue = "";
            if (segment.contains("!")) {
                String[] arr = segment.split("!");
                var = arr[0];
                defaultValue = arr[1];
            }
            template = template.replace("${" + segment + "}", formatSlotItem(params.get(var), defaultValue.replaceAll("'", "")));
        }
        log.debug("after format: {}", template);
        return template;
    }

    /**
     * 格式化占位符
     *
     * @param o
     * @param defaultValue
     * @return
     */
    private String formatSlotItem(Object o, String defaultValue) {
        if (o == null) {
            return defaultValue;
        }
        switch (o.getClass().getSimpleName()) {
            case "String":
            case "Integer":
            case "Double":
            case "Float":
            case "Long":
                return o.toString();
            case "BigDecimal":
                return ((BigDecimal) o).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            default:
                return defaultValue;
        }
    }
}
