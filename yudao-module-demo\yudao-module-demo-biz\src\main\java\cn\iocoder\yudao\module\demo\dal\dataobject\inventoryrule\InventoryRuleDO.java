package cn.iocoder.yudao.module.demo.dal.dataobject.inventoryrule;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 盘点规则 DO
 *
 * <AUTHOR>
 */
@TableName("t_inventory_rule")
@KeySequence("t_inventory_rule_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryRuleDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 规则编码
     */
    private String ruleCode;
    /**
     * 判断类型
     */
    private String judgeType;
    /**
     * 盘点周期数
     */
    private Integer inventoryCycleCount;
    /**
     * 判断周期类型（日、周、月）
     */
    private String cycleType;
    /**
     * 创建人部门ID
     */
    private Long createDeptId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 最后一次执行时间
     */
    private LocalDateTime lastTime;

    /**
     * 规则类型 1-巡检 2-盘点
     */
    private Integer ruleType;

}
