package cn.iocoder.yudao.module.demo.dal.dataobject.materialaccept;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 物资Req表 DO
 *
 * <AUTHOR>
 */
@TableName("t_material_req")
@KeySequence("t_material_req_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialReqDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    private String reqData;

    private String resData;

    private Long businessId;

    private String type;
}
