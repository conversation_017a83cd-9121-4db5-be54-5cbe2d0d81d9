package cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo;

import cn.iocoder.yudao.module.demo.controller.admin.bizflownode.vo.BizFlowNodeRespVO;
import cn.iocoder.yudao.module.demo.controller.admin.materialacceptdetail.vo.MaterialAcceptDetailRespVO;
import cn.iocoder.yudao.module.demo.controller.admin.checkstandard.vo.MaterialCheckStandardRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 物资验收信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MaterialAcceptRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8857")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "验收单号、入库单号、出库单号、物资移位单号、物资盘点单号、巡检单号、维保单号、报废单号、预案单号、隐患单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("验收单号")
    private String acceptNo;

    @Schema(description = "所在单位/责任部门", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("所在单位")
    private String unitName;

    @Schema(description = "单位ID/部门ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "103")
    @ExcelProperty("单位ID")
    private Long unitId;

    @Schema(description = "物资类别/隐患类型（隐患管理）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("物资类别")
    private String materialType;

    @Schema(description = "供货单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("供货单位")
    private String supplier;

    @Schema(description = "直观检验（验收入库）\n维保内容（维修保养）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("直观检验")
    private String visualCheck;

    @Schema(description = "原理检验（验收入库）\n维保结果（维修保养）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("原理检验")
    private String principleCheck;

    @Schema(description = "问题及处理意见/报废原因/适用范围/隐患描述（隐患管理）")
    @ExcelProperty("问题及处理意见")
    private String problemOpinion;

    @Schema(description = "使用说明")
    @ExcelProperty("使用说明")
    private String usageDesc;

    @Schema(description = "验收情况")
    @ExcelProperty("验收情况")
    private String materialSituation;

    @Schema(description = "质量检验")
    @ExcelProperty("质量检验")
    private String qualityInspection;

    @Schema(description = "验收日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("验收日期")
    private LocalDateTime acceptDate;

    @Schema(description = "验收经办人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("验收经办人")
    private String acceptUser;

    @Schema(description = "签名区")
    @ExcelProperty("签名区")
    private String signArea;

    @Schema(description = "附件ID列表")
    @ExcelProperty("附件ID列表")
    private String fileIds;

    @Schema(description = "出库类型")
    @ExcelProperty("出库类型")
    private String outboundType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "物资权属")
    @ExcelProperty("物资权属")
    private String materialBelong;

    @Schema(description = "入库单号")
    @ExcelProperty("入库单号")
    private String inboundNo;

    @Schema(description = "入库类型")
    @ExcelProperty("入库类型")
    private String inboundType;

    @Schema(description = "关联单号")
    @ExcelProperty("关联单号")
    private String relatedNo;

    @Schema(description = "入库仓库ID\\巡检仓库id")
    private Long warehouseId;

    @Schema(description = "入库仓库名称\\巡检仓库名称")
    @ExcelProperty("入库仓库")
    private String warehouseName;

    @Schema(description = "预计入库时间\\预计出库时间")
    @ExcelProperty("预计入库时间")
    private LocalDateTime expectedInboundTime;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "入库经办人/申请人")
    @ExcelProperty("入库经办人")
    private String inboundOperator;

    @Schema(description = "入库经办人签名区")
    @ExcelProperty("入库经办人签名区")
    private String inboundSignArea;

    @Schema(description = "当前流程节点")
    @ExcelProperty("流程节点")
    private Integer flow;

    @Schema(description = "创建部门")
    @ExcelProperty("创建部门ID")
    private Long createDeptId;

    @Schema(description = "物资验收详情列表")
    private List<MaterialAcceptDetailRespVO> details;

    @Schema(description = "物资检查标准列表")
    private List<MaterialCheckStandardRespVO> checkStandards;

    @Schema(description = "业务流程节点列表")
    private List<BizFlowNodeRespVO> flowNodes;

    @Schema(description = "类型 1:物资验收  2：物资入库")
    @ExcelProperty("类型")
    private String type;

    @Schema(description = "物资分类")
    @ExcelProperty("物资分类")
    private String materialClass;

    @Schema(description = "物资规格")
    @ExcelProperty("物资规格")
    private String spec;

    @Schema(description = "单位")
    @ExcelProperty("单位")
    private String unit;

    @Schema(description = "数量/移动数量/报废数量")
    @ExcelProperty("物资数量")
    private Integer quantity;

    @Schema(description = "原id")
    @ExcelProperty("来源ID")
    private Long originId;

    @Schema(description = "调令ID")
    private Long dispatchOrderId;

    @Schema(description = "调令单号")
    @ExcelProperty("调令单号")
    private String dispatchOrderNo;

    @Schema(description = "接收单位")
    @ExcelProperty("接收单位")
    private String receiveUnit;

    @Schema(description = "接收单位ID")
    @ExcelProperty("接收单位ID")
    private Long receiveId;

    @Schema(description = "单位地址")
    @ExcelProperty("单位地址")
    private String unitAddress;

    @Schema(description = "联系人\\巡检人")
    @ExcelProperty("联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    @ExcelProperty("联系电话")
    private String contactPhone;

    @Schema(description = "物资名称\\预案名称")
    @ExcelProperty("物资名称")
    private String materialName;

    @Schema(description = "原货位")
    @ExcelProperty("原货位")
    private String originalLocation;

    @Schema(description = "垛位 /巡检垛位")
    @ExcelProperty("垛位")
    private String location;

    @Schema(description = "垛位ID")
    @ExcelProperty("垛位ID")
    private Long locationId;

    @Schema(description = "目标货位")
    @ExcelProperty("目标货位")
    private String newLocation;

    @Schema(description = "物资编码")
    @ExcelProperty("物资编码")
    private String materialCode;

    @Schema(description = "盘点类型/处理方式（隐患管理）")
    @ExcelProperty("盘点类型")
    private String checkType;

    @Schema(description = "盘点范围/隐患等级（隐患管理）")
    @ExcelProperty("盘点范围")
    private String checkRange;

    @Schema(description = "计划开始时间（）")
    @ExcelProperty("计划开始时间")
    private LocalDateTime planTime;

    @Schema(description = "预案等级")
    @ExcelProperty("预案等级")
    private Integer level;

    @Schema(description = "审核状态 0：待提交1：待审核  2：待上级审核  3：审核驳回 4：审核通过")
    private Integer auditStatus;

    @Schema(description = "巡检状态 1：待执行  2：执行中  3：执行通过")
    @ExcelProperty("巡检状态")
    private Integer inspectionStatus;

    @Schema(description = "生产厂家名称")
    @ExcelProperty("生产厂家名称")
    private String proFactoryName;

    @Schema(description = "生产厂家ID")
    @ExcelProperty("生产厂家ID")
    private Long proFactoryId;

    @Schema(description = "物资种类")
    @ExcelProperty("物资种类")
    private Integer types;

    @Schema(description = "巡检仓库IDs")
    @ExcelProperty("巡检仓库IDs")
    private String inspectionWarehouseIds;

    @Schema(description = "巡检类型")
    @ExcelProperty("巡检类型")
    private String inspectionType;

    private String fileList;

    private String thirdResultFlag;

    @Schema(description = "来源")
    private String dataSource;

    /**
     * 处理措施/上报原因
     */
    @Schema(description = "处理措施/上报原因")
    private String handleContent;

    private String pdaFileUrl;

    private LocalDateTime completeTime;

}
