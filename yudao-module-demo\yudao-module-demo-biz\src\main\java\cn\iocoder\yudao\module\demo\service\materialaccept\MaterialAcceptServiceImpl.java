package cn.iocoder.yudao.module.demo.service.materialaccept;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.demo.controller.admin.homepage.vo.HomepageMaterialVO;
import cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo.*;
import cn.iocoder.yudao.module.demo.controller.admin.materialacceptdetail.vo.MaterialAcceptDetailSaveReqVO;
import cn.iocoder.yudao.module.demo.dal.dataobject.bizflownode.BizFlowNodeDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.dispatchorder.DispatchOrderDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialaccept.MaterialAcceptDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialacceptdetail.MaterialAcceptDetailDO;
import cn.iocoder.yudao.module.demo.dal.mysql.bizflownode.BizFlowNodeMapper;
import cn.iocoder.yudao.module.demo.dal.mysql.dispatchorder.DispatchOrderMapper;
import cn.iocoder.yudao.module.demo.dal.mysql.materialaccept.MaterialAcceptMapper;
import cn.iocoder.yudao.module.demo.dal.mysql.materialacceptdetail.MaterialAcceptDetailMapper;
import cn.iocoder.yudao.module.demo.framework.demodatapermission.config.DemoDataPermissionRule;
import cn.iocoder.yudao.module.demo.service.async.AsyncMaterialService;
import cn.iocoder.yudao.module.demo.util.DataPermissionUtil;
import cn.iocoder.yudao.module.demo.util.ToolUtils;
import cn.iocoder.yudao.module.demo.util.WordUtil;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.service.dept.DeptService;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.demo.enums.ErrorCodeConstants.*;

/**
 * 物资验收 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MaterialAcceptServiceImpl implements MaterialAcceptService {

    @Resource
    private MaterialAcceptMapper materialAcceptMapper;

    @Resource
    private MaterialAcceptDetailMapper materialAcceptDetailMapper;

    @Resource
    private BizFlowNodeMapper bizFlowNodeMapper;

    @Resource
    private PermissionService permissionService;

    @Resource
    private DeptService deptService;

    @Resource
    @Lazy
    private AsyncMaterialService asyncMaterialService;
    @Resource
    private DispatchOrderMapper dispatchOrderMapper;

    @Resource
    private DemoDataPermissionRule dataPermissionRule;

    @Resource
    private WordUtil wordUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createMaterialAccept(MaterialAcceptSaveReqVO createReqVO) {
        preHandle(createReqVO);

        Long deptId = null ;
        if (createReqVO.getCreateDeptId() == null) {
            deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        }else {
            deptId = createReqVO.getCreateDeptId();
        }
        String type = createReqVO.getType();
        // 插入
        MaterialAcceptDO materialAccept = BeanUtils.toBean(createReqVO, MaterialAcceptDO.class);
        // 确保不设置ID，让数据库自动生成
        materialAccept.setId(null);
        materialAccept.setCreateDeptId(deptId);

        // 生成验收单号
        String typeStr = "YS";
        if (createReqVO.getType().equals("物资入库")){
            typeStr = "RK";
        }else if (createReqVO.getType().equals("调运/前置出库")){
            typeStr = "CK";
        }else if (createReqVO.getType().equals("其他出库")){
            typeStr = "QTCK";
        }else if (createReqVO.getType().equals("物资移位")){
            typeStr = "YW";
        }else if (createReqVO.getType().equals("物资盘点")) {
            createReqVO.setInspectionType("自主巡检");
            typeStr = "PD";
        }else if (createReqVO.getType().equals("报废处置")) {
            typeStr = "BF";
        }else if (createReqVO.getType().equals("预警处置")) {
            typeStr = "YJ";
        }else if (createReqVO.getType().equals("隐患处置")) {
            typeStr = "YH";
        }else if (createReqVO.getType().equals("巡检盘点")) {
            typeStr = "XJ";
            createReqVO.setInspectionType("自主巡检");
        }else if (createReqVO.getType().equals("维修保养")) {
            typeStr = "WB";
        }
        DeptDO dept = deptService.getDept(deptId);

        // 获取单号前缀
        String prefix = ToolUtils.generateOrderNoPrefix(dept.getName(), typeStr);
        // 从数据库获取最大序号
        Integer maxSequence = materialAcceptMapper.selectMaxSequenceByPrefix(prefix);
        // 生成验收单号
        String acceptNo = ToolUtils.generateOrderNo(dept.getName(), typeStr, maxSequence);

        materialAccept.setAcceptNo(acceptNo);
        materialAccept.setAuditStatus(createReqVO.getFlow());
        //将物资详情信息同步至物资表中
        tbDetails(materialAccept,createReqVO.getDetails());
        //处理特殊业务逻辑
        handleBusiness(createReqVO, materialAccept);

        materialAcceptMapper.insert(materialAccept);
        // 保存详情列表
        saveMaterialAcceptDetails(materialAccept.getId(), createReqVO.getDetails());

        // 判断是否提交流程
        if (createReqVO.getFlow() != null && createReqVO.getFlow() == 1) {
            handleBizFlowNodes(deptId, type, materialAccept);
        }

        //如果是巡检或者盘点，则传到第三方
        if ("巡检盘点".equals(type) || "物资盘点".equals(type)) {
            asyncMaterialService.sendMaterialAcceptDataToExternalApi(materialAccept.getId(), type);
        }

        afterHandle(createReqVO, materialAccept);

        // 返回
        return materialAccept.getId();
    }

    private void afterHandle(MaterialAcceptSaveReqVO createReqVO, MaterialAcceptDO materialAccept) {
        if ("调运/前置出库".equals(createReqVO.getType())) {
            //修改关联状态
            dispatchOrderMapper.update(Wrappers.<DispatchOrderDO>lambdaUpdate()
                    .eq(DispatchOrderDO::getOrderNo, createReqVO.getRelatedNo())
                    .set(DispatchOrderDO::getStatus, 1));
        }
    }

    private void preHandle(MaterialAcceptSaveReqVO createReqVO) {
        if ("物资盘点".equals(createReqVO.getType())) {
            preHandleWzpd(createReqVO);
        }
    }

    private void preHandleWzpd(MaterialAcceptSaveReqVO createReqVO) {
        if ("抽样盘点".equals(createReqVO.getOutboundType()) || Objects.isNull(createReqVO.getOutboundType())) {
            return;
        }

        Long unitId = createReqVO.getUnitId();
        Long warehouseId = createReqVO.getWarehouseId();

        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Long roleId = DataPermissionUtil.getDataRole(userId, permissionService);

        String body = "{\"data\":{\"roleId\":"+roleId+",\"unitId\":"+deptId+",\"pageNum\":1,\"pageSize\":9999}}";

        String responseStr = HttpUtil.createPost("http://192.168.195.192:8082/sl/anlante/assets/info/list")
                .body(body)
                .execute()
                .body();
        JSONObject jsonObject = JSON.parseObject(responseStr);
        JSONArray rows = jsonObject.getJSONArray("rows");

        if (Objects.isNull(rows)) {
            return;
        }

//        System.out.println("示例数据: " + rows.getJSONObject(0).toJSONString());

//        新增比对，过滤原始 rows，确保 unitId 和 warehouseId 匹配
        JSONArray filteredRows = new JSONArray();
        for (Object obj : rows) {
            JSONObject row = (JSONObject) obj;

            Long respUnitId = row.getLong("unitId");
            Long respWarehouseId = row.getLong("warehouseId");

            if (respUnitId != null && respWarehouseId != null
                    && respUnitId.equals(unitId)
                    && respWarehouseId.equals(warehouseId)) {
                filteredRows.add(row);
            }
        }
        if (filteredRows.isEmpty()) {
            throw new RuntimeException("未找到与指定仓库ID(" + warehouseId + ")和单位ID(" + unitId + ")匹配的物资数据");
        }
        rows = filteredRows;

        List<MaterialAcceptDetailSaveReqVO> materialVOList = JSON.parseArray(JSON.toJSONString(rows), MaterialAcceptDetailSaveReqVO.class);
        if (CollectionUtils.isEmpty(materialVOList)) {
            throw new RuntimeException("物资为空");
        }

        for (MaterialAcceptDetailSaveReqVO materialAcceptDetailSaveReqVO : materialVOList) {
            materialAcceptDetailSaveReqVO.setMaterialModel(materialAcceptDetailSaveReqVO.getModel());
        }

        createReqVO.setDetails(materialVOList);
    }

    private void handleBusiness(MaterialAcceptSaveReqVO createReqVO, MaterialAcceptDO materialAccept) {
        if ("巡检盘点".equals(createReqVO.getType())) {
            materialAccept.setInspectionType("自主巡检");
        }
    }

    /**
     * 将物资信息同步至物资表中
     * @param materialAccept 物资验收对象
     * @param details 物资验收详情列表
     */
    private void tbDetails(final MaterialAcceptDO materialAccept, final List<MaterialAcceptDetailSaveReqVO> details) {
        if (details == null || details.isEmpty()) {
            return;
        }

        // 获取第一条详情记录
        MaterialAcceptDetailSaveReqVO firstDetail = details.get(0);

        // 设置基本物资信息
        materialAccept.setMaterialClass(firstDetail.getMaterialClass());
        materialAccept.setSpec(firstDetail.getSpec());
        materialAccept.setUnit(firstDetail.getUnit());
        materialAccept.setMaterialName(firstDetail.getMaterialName());
        materialAccept.setMaterialCode(firstDetail.getMaterialCode());

        // 计算总数量
        int quantity = details.stream()
                .mapToInt(detail -> detail.getQuantity() != null ? detail.getQuantity() : 0)
                .sum();
        materialAccept.setQuantity(quantity);
        //添加总数
        materialAccept.setTypes(details.size());
        //列表展示物资所在仓库 仓库名称去重用逗号隔开
        String warehouseNames = details.stream()
                .map(MaterialAcceptDetailSaveReqVO::getWarehouse)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.joining(","));
        materialAccept.setWarehouseName(warehouseNames);
        // 验证数量是否匹配
        if (materialAccept.getOriginId() != null && materialAccept.getQuantity() != quantity) {
            throw exception(MATERIAL_ACCEPT_QUANTITY_ERROR);
        }

        // 如果是物资移位，更新原货位和目标货位
        if ("物资移位".equals(materialAccept.getType())) {
            String warehouse = firstDetail.getWarehouse() != null ? firstDetail.getWarehouse() : "";
            String location = firstDetail.getLocation() != null ? firstDetail.getLocation() : "";
            String targetWarehouse = firstDetail.getTargetWarehouse() != null ? firstDetail.getTargetWarehouse() : "";
            String targetLocation = firstDetail.getTargetLocation() != null ? firstDetail.getTargetLocation() : "";

            materialAccept.setOriginalLocation(warehouse + "-" + location);
            materialAccept.setNewLocation(targetWarehouse + "-" + targetLocation);
        }
    }

    @Override
    public void updateMaterialAccept(MaterialAcceptSaveReqVO updateReqVO) {
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        String type = updateReqVO.getType();

        // 校验必要参数
        if (deptId == null) {
            throw new IllegalArgumentException("当前用户未关联部门信息");
        }
        if (type == null || type.trim().isEmpty()) {
            throw new IllegalArgumentException("物资类型不能为空");
        }

        // 校验存在
        validateMaterialAcceptExists(updateReqVO.getId());
        //查询历史数据
        MaterialAcceptDO old = materialAcceptMapper.selectById(updateReqVO.getId());
        if (old == null) {
            throw exception(MATERIAL_ACCEPT_NOT_EXISTS);
        }
        // 更新
        MaterialAcceptDO updateObj = BeanUtils.toBean(updateReqVO, MaterialAcceptDO.class);
        updateObj.setAuditStatus(updateReqVO.getFlow());
        tbDetails(updateObj,updateReqVO.getDetails());
        materialAcceptMapper.updateById(updateObj);
        updateYsInfoForOriginId(updateObj);

        // 判断是否提交流程
        Integer oldFlow = old.getFlow();
        Integer oldAuditStatus = old.getAuditStatus();

        if ((oldFlow == null || oldFlow == 0) && updateReqVO.getFlow() != null && updateReqVO.getFlow() == 1) {
            handleBizFlowNodes(deptId, type, updateObj);

        } else if (oldAuditStatus != null && oldAuditStatus == 3 && updateReqVO.getFlow() != null && updateReqVO.getFlow() == 1) {
            //被驳回,将原先流程节点删除 在新增
            bizFlowNodeMapper.deleteByBusinessId(updateReqVO.getId(), type);
            handleBizFlowNodes(deptId, type, updateObj);
        }
        // 这里需要处理详情的删除。简单方式是先逻辑删除所有旧详情，再插入/更新新的。
        materialAcceptDetailMapper.deleteByAcceptId(updateReqVO.getId()); // 逻辑删除旧详情
        saveMaterialAcceptDetails(updateReqVO.getId(), updateReqVO.getDetails()); // 保存新详情
    }

    /**
     * 根据原id判断是否要更新
     * @param updateObj
     */
    private void updateYsInfoForOriginId(final MaterialAcceptDO updateObj) {
        //判断是否将入库验收情况、质量检验更新
        if (updateObj.getOriginId()!=null){
            MaterialAcceptDO materialAcceptDO = new MaterialAcceptDO();
            materialAcceptDO.setId(updateObj.getOriginId());
            materialAcceptDO.setMaterialSituation(updateObj.getMaterialSituation());
            materialAcceptDO.setQualityInspection(updateObj.getQualityInspection());
            materialAcceptMapper.updateById(materialAcceptDO);
        }
    }

    @Override
    public void deleteMaterialAccept(Long id) {
        // 校验存在
        validateMaterialAcceptExists(id);
        // 删除
        materialAcceptMapper.deleteById(id);
    }

    private void validateMaterialAcceptExists(Long id) {
        if (materialAcceptMapper.selectById(id) == null) {
            throw exception(MATERIAL_ACCEPT_NOT_EXISTS);
        }
    }

    @Override
    public MaterialAcceptDO getMaterialAccept(Long id) {
        // 查询主表
        MaterialAcceptDO materialAccept = materialAcceptMapper.selectById(id);
        if (materialAccept == null) {
            return null; // 或者抛出异常
        }

        // 查询关联的详情列表
        List<MaterialAcceptDetailDO> details = getMaterialAcceptDetailsByAcceptId(id);
        // 将详情列表设置到主表对象中 (需要在主表DO中增加 details 字段)
        // 由于主表DO不方便直接嵌套列表，通常在 Service 返回 VO 时进行合并
        // 这里 Service 返回 DO，Controller 负责转 VO 并合并详情
        // materialAccept.setDetails(details); // 如果 DO 中有 details 字段

        return materialAccept; // 返回主表 DO
    }

    @Override
    public PageResult<MaterialAcceptDO> getMaterialAcceptPage(MaterialAcceptPageReqVO pageReqVO) {
        return materialAcceptMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<MaterialAcceptWithFlowRespVO> getMaterialAcceptWithFlowPage(MaterialAcceptPageReqVO pageReqVO) {

        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();

        // 1.2 获得角色列表
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(getLoginUserId());
        // 特殊：不分页，直接查询全部
        if (PageParam.PAGE_SIZE_NONE.equals(pageReqVO.getPageSize())) {
            List<MaterialAcceptWithFlowRespVO> list = materialAcceptMapper.selectPageWithFlow(pageReqVO, deptId, roleIds);
            return new PageResult<>(list, (long) list.size());
        }
        //查询自己已经处理过的数据及待我处理的数据
        //判断领导角色只查询已通过数据
        if (permissionService.hasAnyRoles(SecurityFrameworkUtils.getLoginUserId(), "leader")) {
            pageReqVO.setAuditStatus(4);
        } else if (!permissionService.hasAnyRoles(SecurityFrameworkUtils.getLoginUserId(), "warehouse_keeper")
            && !permissionService.hasAnyRoles(SecurityFrameworkUtils.getLoginUserId(), "super_admin")
            && !permissionService.hasAnyRoles(SecurityFrameworkUtils.getLoginUserId(), "child_admin")){
            //根据用户id 及部门id和type 去查询对应的 orderNo

            String bizType = pageReqVO.getType();

            // 如果type包含"出库"，统一处理为"物资出库"类型
            if (bizType != null && bizType.contains("出库")) {
                bizType = "物资出库";
            }

            // 查询当前用户可以处理的最高优先级流程节点
            Integer orderNo = bizFlowNodeMapper.selectMaxOrderNoByUserAndDeptAndType(userId, deptId, bizType);

            if (orderNo != null && orderNo > 1) {
                pageReqVO.setFlow(orderNo);
            } else if (orderNo == null) {
                return new PageResult<>(new ArrayList<>(), 0L);
            }
        }

        if (permissionService.hasAnyRoles(SecurityFrameworkUtils.getLoginUserId(),
                "warehouse_keeper","","deputy_director","director","child_admin")) {
            pageReqVO.setCreateDeptId(deptId);
        }

        // 查询总记录数
        Long count = materialAcceptMapper.selectCountWithFlow(pageReqVO,deptId, roleIds);
        if (count == 0) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }

        // 在Java中计算偏移量，避免在SQL中使用表达式
        Integer pageNo = pageReqVO.getPageNo();
        Integer pageSize = pageReqVO.getPageSize();
        Integer offset = (pageNo - 1) * pageSize;

        // 创建新的查询参数对象，包含计算好的偏移量
        MaterialAcceptPageReqVO newPageReqVO = new MaterialAcceptPageReqVO();
        BeanUtils.copyProperties(pageReqVO, newPageReqVO);
        newPageReqVO.setOffset(offset);

        // 查询分页数据
        List<MaterialAcceptWithFlowRespVO> list = materialAcceptMapper.selectPageWithFlow(newPageReqVO, deptId, roleIds);
        if (list == null) {
            return new PageResult<>(Collections.emptyList(), count);
        }

        //查询当前用户审核流程节点
        for (MaterialAcceptWithFlowRespVO materialAccept : list) {
            if (materialAccept != null && materialAccept.getAuditStatus() != null && materialAccept.getAuditStatus() == 1) {
                if (materialAccept.getDeptId() != null && materialAccept.getDeptId().equals(deptId)
                        && materialAccept.getRoleId() != null && roleIds.contains(materialAccept.getRoleId())) {
                    materialAccept.setAuditStatus(1);
                } else {
                    materialAccept.setAuditStatus(2);
                }
            }
        }


        return new PageResult<>(list, count);
    }

    // ========== 子表操作实现 ==========

    @Override
    public void saveMaterialAcceptDetails(Long acceptId, List<MaterialAcceptDetailSaveReqVO> detailList) {
        if (detailList == null || detailList.isEmpty()) {
            return;
        }
        for (MaterialAcceptDetailSaveReqVO detailReqVO : detailList) {
            MaterialAcceptDetailDO detailDO = BeanUtils.toBean(detailReqVO, MaterialAcceptDetailDO.class);
            detailDO.setAcceptId(acceptId); // 设置关联主表ID

            // if (detailDO.getId() == null) { // 无 ID，代表新增
            //     // 确保不设置ID，让数据库自动生成
                detailDO.setId(null);
                materialAcceptDetailMapper.insert(detailDO);
            // } else { // 有 ID，代表已存在，进行更新
            //     // TODO: 校验详情是否存在且属于该主表
            //     materialAcceptDetailMapper.updateById(detailDO);
            // }

        }
    }

    @Override
    public List<MaterialAcceptDetailDO> getMaterialAcceptDetailsByAcceptId(Long acceptId) {
        return materialAcceptDetailMapper.selectListByAcceptId(acceptId);
    }

    /**
     * 处理业务流程节点
     *
     * @param deptId 部门ID
     * @param type 业务类型
     * @param updateObj 更新对象
     */
    @Override
    public void handleBizFlowNodes(Long deptId, String type, MaterialAcceptDO updateObj) {
        //直接审批通过
        if ("隐患处置".equals(updateObj.getType()) && "self".equals(updateObj.getCheckType())) {
            MaterialAcceptDO materialAccept = new MaterialAcceptDO();
            materialAccept.setAuditStatus(4);
            materialAccept.setId(updateObj.getId());
            materialAcceptMapper.updateById(materialAccept);

            return;
        }

        if (type.contains("出库")){
            type = "物资出库";
        }
        // 根据部门和物资类型查询业务流程节点
        List<BizFlowNodeDO> flowNodeList = bizFlowNodeMapper.selectListByCreateDeptAndBizType(
                deptId,
                type // 固定业务类型为"物资验收、物资入库"
        );
        //查询出flowNodeList中最大OrderNo
        Integer maxOrderNo = flowNodeList.stream()
                .map(BizFlowNodeDO::getOrderNo)
                .filter(Objects::nonNull)
                .max(Integer::compareTo)
                .orElse(null);
        MaterialAcceptDO materialAccept = new MaterialAcceptDO();
        materialAccept.setFlow(maxOrderNo);
        materialAccept.setId(updateObj.getId());
        materialAcceptMapper.updateById(materialAccept);

        // 处理流程节点
        if (flowNodeList != null && !flowNodeList.isEmpty()) {
            //遍历流程节点
            for (BizFlowNodeDO flowNode : flowNodeList) {
                flowNode.setCreateTime(LocalDateTime.now());
                flowNode.setUpdateTime(null);
                if (flowNode.getOrderNo()==1) {
                    //自动通过
                    flowNode.setUpdateTime(LocalDateTime.now());
                    flowNode.setIsPass(1);
                    flowNode.setAuditPerson(updateObj.getAcceptUser());
                    flowNode.setAuditSign(updateObj.getSignArea());
                    flowNode.setBusinessId(updateObj.getId());
                    flowNode.setAuditPersonId(getLoginUserId());
                }
                flowNode.setId(null);
                flowNode.setBusinessId(updateObj.getId());
                //新增流程节点
                bizFlowNodeMapper.insert(flowNode);
            }
        }
    }

    @Override
    public List<MaterialAcceptWithFlowRespVO> getInventory() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();

        List<Long> deptIdList = DataPermissionUtil.getUserScope(permissionService, dataPermissionRule, userId, deptId);

        return materialAcceptMapper.getInventory(deptIdList);
    }

    @Override
    public void downloadReceiveReport(HttpServletResponse response) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("field01", "test");

        wordUtil.createOss(dataModel, "省级水旱灾害防御物资入库验收单.docx", "省级水旱灾害防御物资入库验收单.docx");
    }

    /**
     * 出库类型
     */
    private String outboundType;

    @Override
    public PageResult<MaterialAcceptApprovedRespVO> getApprovedMaterialAcceptPage(MaterialAcceptApprovedPageReqVO pageReqVO) {
        //设置过滤条件
        // 获取当前登录用户ID
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();
        // 计算分页参数
        int offset = (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize();
        // 创建新的查询参数对象，包含计算好的偏移量
        MaterialAcceptApprovedPageReqVO newPageReqVO = new MaterialAcceptApprovedPageReqVO();
        BeanUtils.copyProperties(pageReqVO, newPageReqVO);
        // 设置偏移量
        newPageReqVO.setOffset(offset);
        // 判断用户角色，确定权限类型
        if (permissionService.hasAnyRoles(userId, "all_data") || permissionService.hasAnyRoles(userId, "super_admin")) {

        } else if (permissionService.hasAnyRoles(userId, "below_dept_data")) {
            // 本部门及下级部门数据权限
            // 本部门及下级部门数据
            List<Long> deptIds = dataPermissionRule.getDeptAndChildDeptIds(deptId);

            // 如果部门列表为空，添加一个不存在的部门ID，确保查不到数据
            if (CollUtil.isEmpty(deptIds)) {
                deptIds.add(-1L);
            }

            pageReqVO.setCreateDeptIdList(deptIds);
        }  else {
            newPageReqVO.setCreateDeptId(deptId);
        }
        // 查询结果
        List<MaterialAcceptApprovedRespVO> list = materialAcceptMapper.selectApprovedPage(newPageReqVO);
        Long total = materialAcceptMapper.selectApprovedCount(newPageReqVO);
        return new PageResult<>(list, total);
    }

    @Override
    public MaterialAcceptWarningStatsRespVO getWarningStats(Long deptId) {
        // 一次性查询所有统计数据（包含处理率）
        MaterialAcceptWarningStatsRespVO statsRespVO = materialAcceptMapper.getWarningStatsByDeptId(deptId);

        return statsRespVO;
    }

    @Override
    public WarehouseStatsRespVO getWarehouseStats(Long warehouseId) {
        // 使用MaterialAcceptMapper查询仓库统计信息
        return materialAcceptMapper.selectWarehouseStats(warehouseId);
    }

}
