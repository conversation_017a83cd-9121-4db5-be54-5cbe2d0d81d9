package cn.iocoder.yudao.module.demo.openapi.service.impl;

import cn.iocoder.yudao.module.demo.dal.dataobject.checkstandard.MaterialCheckStandardDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialaccept.MaterialAcceptDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialacceptdetail.MaterialAcceptDetailDO;
import cn.iocoder.yudao.module.demo.dal.mysql.checkstandard.MaterialCheckStandardMapper;
import cn.iocoder.yudao.module.demo.dal.mysql.materialaccept.MaterialAcceptMapper;
import cn.iocoder.yudao.module.demo.dal.mysql.materialacceptdetail.MaterialAcceptDetailMapper;
import cn.iocoder.yudao.module.demo.openapi.service.MaterialAcceptReturnService;
import cn.iocoder.yudao.module.demo.openapi.vo.MaterialAcceptDetailVO;
import cn.iocoder.yudao.module.demo.openapi.vo.MaterialAcceptReturnVO;
import cn.iocoder.yudao.module.demo.service.async.AsyncMaterialService;
import cn.iocoder.yudao.module.demo.service.materialaccept.MaterialAcceptService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

@Service
public class MaterialAcceptReturnServiceImpl implements MaterialAcceptReturnService {

    @Resource
    private MaterialAcceptService materialAcceptService;
    @Resource
    private MaterialAcceptMapper materialAcceptMapper;
    @Resource
    private MaterialAcceptDetailMapper materialAcceptDetailMapper;
    @Resource
    private MaterialCheckStandardMapper materialCheckStandardMapper;

    @Override
    @Transactional
    public void accept(MaterialAcceptReturnVO updateReqVO) {
        String type = updateReqVO.getType();

        MaterialAcceptDO exist = materialAcceptService.getMaterialAccept(updateReqVO.getId());
        if (Objects.isNull(exist)) {
            throw new RuntimeException("根据id查询数据不存在");
        }
        if (!exist.getType().equals(type)) {
            throw new RuntimeException("type不正确");
        }

        if ("巡检盘点".equals(type) || "物资盘点".equals(type)) {
            handleStocking(updateReqVO, exist);
        } else {
            handleMeterial(updateReqVO, exist);
        }
    }

    private void handleMeterial(MaterialAcceptReturnVO updateReqVO, MaterialAcceptDO exist) {
        //更新主业务字段
        MaterialAcceptDO materialAcceptDO = new MaterialAcceptDO();
        materialAcceptDO.setId(updateReqVO.getId());
        materialAcceptDO.setThirdResultFlag("1");
        materialAcceptDO.setCompleteTime(LocalDateTime.now());

        if (CollectionUtils.isNotEmpty(updateReqVO.getFileList())) {
            materialAcceptDO.setFileList(JSON.toJSONString(updateReqVO.getFileList()));
        }

        materialAcceptMapper.updateById(materialAcceptDO);
    }

    private void handleStocking(MaterialAcceptReturnVO updateReqVO, MaterialAcceptDO exist) {
        if (exist.getAuditStatus() > 0) {
            throw new RuntimeException("数据已提交审核, 无法更新");
        }

        //更新主业务字段
        MaterialAcceptDO materialAcceptDO = new MaterialAcceptDO();
        materialAcceptDO.setId(updateReqVO.getId());
        if (CollectionUtils.isNotEmpty(updateReqVO.getFileList())) {
            materialAcceptDO.setFileList(JSON.toJSONString(updateReqVO.getFileList()));
        }
        materialAcceptDO.setFlow(1);
        materialAcceptDO.setThirdResultFlag("1");
        materialAcceptDO.setRemark(updateReqVO.getRemark());
        materialAcceptDO.setCompleteTime(LocalDateTime.now());

        if (updateReqVO.getFlow() == 0) {
            materialAcceptDO.setAuditStatus(4);
        } else {
            materialAcceptDO.setAuditStatus(1);
        }

        materialAcceptMapper.updateById(materialAcceptDO);

        if (CollectionUtils.isNotEmpty(updateReqVO.getDetailList())) {
            if ("巡检盘点".equals(updateReqVO.getType())) {
                for (MaterialAcceptDetailVO detailVO : updateReqVO.getDetailList()) {
                    MaterialCheckStandardDO materialCheckStandardDO = new MaterialCheckStandardDO();
                    MaterialCheckStandardDO existDetail = materialCheckStandardMapper.selectById(detailVO.getId());
                    if (Objects.isNull(existDetail)) {
                        throw new RuntimeException("根据明细id查询明细不存在");
                    }

                    materialCheckStandardDO.setId(detailVO.getId());
                    materialCheckStandardDO.setCheckResult(detailVO.getCheckResult());

                    materialCheckStandardMapper.updateById(materialCheckStandardDO);
                }
            } else {
                for (MaterialAcceptDetailVO detailVO : updateReqVO.getDetailList()) {
                    MaterialAcceptDetailDO materialAcceptDetailDO = new MaterialAcceptDetailDO();
                    MaterialAcceptDetailDO existDetail = materialAcceptDetailMapper.selectById(detailVO.getId());
                    if (Objects.isNull(existDetail)) {
                        throw new RuntimeException("根据明细id查询明细不存在");
                    }

                    materialAcceptDetailDO.setId(detailVO.getId());
                    materialAcceptDetailDO.setCheckResult(detailVO.getCheckResult());

                    materialAcceptDetailMapper.updateById(materialAcceptDetailDO);
                }
            }
        }

        if (updateReqVO.getFlow() == 1) {
            materialAcceptService.handleBizFlowNodes(exist.getCreateDeptId(), exist.getType(), exist);
        }
    }
}
