package cn.iocoder.yudao.module.demo.job;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo.MaterialAcceptSaveReqVO;
import cn.iocoder.yudao.module.demo.service.materialaccept.MaterialAcceptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import cn.iocoder.yudao.module.demo.service.inventoryrule.InventoryRuleService;
import cn.iocoder.yudao.module.demo.dal.dataobject.inventoryrule.InventoryRuleDO;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 盘点规则定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 14:32
 */
@Component
@Slf4j
public class InventoryRuleJob implements JobHandler {

    @Autowired
    private InventoryRuleService inventoryRuleService;

    @Autowired
    private MaterialAcceptService materialAcceptService;

    @Override
    public String execute(final String param) throws Exception {
        log.info("开始执行盘点规则定时任务");

        try {
            // 查询所有盘点规则
            List<InventoryRuleDO> ruleList = inventoryRuleService.getAllRules();
            log.info("查询到{}条盘点规则", ruleList.size());

            LocalDateTime now = LocalDateTime.now();
            int executedCount = 0;

            for (InventoryRuleDO rule : ruleList) {
                try {
                    if (shouldExecuteRule(rule, now)) {
                        executeInventoryRule(rule, now);
                        executedCount++;
                    }
                } catch (Exception e) {
                    log.error("执行盘点规则[{}]失败", rule.getRuleName(), e);
                }
            }

            log.info("盘点规则定时任务执行完成，共执行{}条规则", executedCount);
            return String.format("盘点规则定时任务执行完成，共执行%d条规则", executedCount);

        } catch (Exception e) {
            log.error("盘点规则定时任务执行异常", e);
            throw e;
        }
    }

    /**
     * 判断规则是否应该执行
     */
    private boolean shouldExecuteRule(InventoryRuleDO rule, LocalDateTime now) {
        LocalDateTime lastTime = rule.getLastTime();
        int cycleCount = rule.getInventoryCycleCount();
        String cycleType = rule.getCycleType();

        // 计算下次应执行时间
        LocalDateTime nextTime;
        if (lastTime == null) {
            // 没有lastTime，强制执行
            return true;
        } else if ("日".equals(cycleType)) {
            nextTime = lastTime.plusDays(cycleCount);
        } else if ("周".equals(cycleType)) {
            nextTime = lastTime.plusWeeks(cycleCount);
        } else if ("月".equals(cycleType)) {
            nextTime = lastTime.plusMonths(cycleCount);
        } else {
            log.warn("未知的周期类型[{}]，规则[{}]", cycleType, rule.getRuleName());
            return false;
        }

        return now.isAfter(nextTime);
    }

    /**
     * 执行盘点规则
     */
    private void executeInventoryRule(InventoryRuleDO rule, LocalDateTime now) {
        log.info("开始执行盘点规则[{}]", rule.getRuleName());

        try {
            // TODO: 调用你的addInven方法，传递rule等参数
            addEvent(rule);

            // 更新lastTime
            inventoryRuleService.updateRuleLastTime(rule.getId(), now);

            log.info("盘点规则[{}]执行完成", rule.getRuleName());
        } catch (Exception e) {
            log.error("盘点规则[{}]执行失败", rule.getRuleName(), e);
            throw e;
        }
    }

    /**
     * 添加巡检、盘点任务
     * @param rule
     */
    private void addEvent(final InventoryRuleDO rule) {
        // 新增巡检
        if (rule.getRuleType() == 1) {
            MaterialAcceptSaveReqVO xjReqVO = new MaterialAcceptSaveReqVO();
            xjReqVO.setType("巡检盘点");
            xjReqVO.setCreateDeptId(rule.getCreateDeptId());
            xjReqVO.setUnitId(rule.getCreateDeptId());
            xjReqVO.setInspectionType(rule.getCycleType() + "巡检");
            materialAcceptService.createMaterialAccept(xjReqVO);
        } else {
            //新增盘点
            MaterialAcceptSaveReqVO pdReqVO = new MaterialAcceptSaveReqVO();
            pdReqVO.setType("物资盘点");
            pdReqVO.setCreateDeptId(rule.getCreateDeptId());
            pdReqVO.setUnitId(rule.getCreateDeptId());
            pdReqVO.setInspectionType(rule.getCycleType()+"巡检");
            materialAcceptService.createMaterialAccept(pdReqVO);
        }
    }
}
