package cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 已审核通过的物资验收信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MaterialAcceptApprovedRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8857")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "验收单号、入库单号、出库单号、物资移位单号、物资盘点单号、巡检单号、维保单号、报废单号、预案单号、隐患单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("验收单号")
    private String acceptNo;

    @Schema(description = "所在单位/责任部门", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("所在单位")
    private String unitName;

    @Schema(description = "类型 1:物资验收 2：物资入库", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("类型")
    private String type;

    @Schema(description = "审核通过时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("审核通过时间")
    private LocalDateTime approvedTime;

    @Schema(description = "巡检状态 1：待执行  2：执行中  3：执行通过")
//    @ExcelProperty("巡检状态")
    private Integer inspectionStatus;

    @Schema(description = "巡检仓库IDs")
//    @ExcelProperty("巡检仓库IDs")
    private String inspectionWarehouseIds;

}
