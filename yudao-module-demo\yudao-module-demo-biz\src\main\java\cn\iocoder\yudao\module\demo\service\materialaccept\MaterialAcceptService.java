package cn.iocoder.yudao.module.demo.service.materialaccept;

import java.util.*;
import javax.servlet.http.HttpServletResponse;
import javax.validation.*;
import cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo.*;
import cn.iocoder.yudao.module.demo.controller.admin.materialacceptdetail.vo.MaterialAcceptDetailSaveReqVO;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialaccept.MaterialAcceptDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialacceptdetail.MaterialAcceptDetailDO;

/**
 * 物资验收信息 Service 接口
 *
 * <AUTHOR>
 */
public interface MaterialAcceptService {

    /**
     * 创建物资验收信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMaterialAccept(@Valid MaterialAcceptSaveReqVO createReqVO);

    /**
     * 更新物资验收信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMaterialAccept(@Valid MaterialAcceptSaveReqVO updateReqVO);

    /**
     * 删除物资验收信息
     *
     * @param id 编号
     */
    void deleteMaterialAccept(Long id);

    /**
     * 获得物资验收信息
     *
     * @param id 编号
     * @return 物资验收信息
     */
    MaterialAcceptDO getMaterialAccept(Long id);

    /**
     * 获得物资验收信息分页
     *
     * @param pageReqVO 分页查询
     * @return 物资验收信息分页
     */
    PageResult<MaterialAcceptDO> getMaterialAcceptPage(MaterialAcceptPageReqVO pageReqVO);

    /**
     * 获得物资验收信息与流程节点的关联分页
     *
     * @param pageReqVO 分页查询
     * @return 物资验收信息与流程节点的关联分页
     */
    PageResult<MaterialAcceptWithFlowRespVO> getMaterialAcceptWithFlowPage(MaterialAcceptPageReqVO pageReqVO);

    /**
     * 获得已审核通过的物资信息分页
     *
     * @param pageReqVO 分页查询
     * @return 已审核通过的物资信息分页
     */
    PageResult<MaterialAcceptApprovedRespVO> getApprovedMaterialAcceptPage(MaterialAcceptApprovedPageReqVO pageReqVO);

    /**
     * 保存物资验收详情列表（新增或更新）
     * @param acceptId 主表 ID
     * @param detailList 详情列表
     */
    void saveMaterialAcceptDetails(Long acceptId, List<MaterialAcceptDetailSaveReqVO> detailList);

    /**
     * 获取物资验收详情列表
     * @param acceptId 主表 ID
     * @return 详情列表
     */
    List<MaterialAcceptDetailDO> getMaterialAcceptDetailsByAcceptId(Long acceptId);

    /**
     * 获取预警处置数据统计
     * @param deptId 部门ID
     * @return 预警处置数据统计
     */
    MaterialAcceptWarningStatsRespVO getWarningStats(Long deptId);

    /**
     * 获取仓库统计信息
     * @param warehouseId 仓库ID
     * @return 仓库统计信息
     */
    WarehouseStatsRespVO getWarehouseStats(Long warehouseId);

    void handleBizFlowNodes(Long deptId, String type, MaterialAcceptDO updateObj);

    List<MaterialAcceptWithFlowRespVO> getInventory();

    void downloadReceiveReport(HttpServletResponse response);
}
