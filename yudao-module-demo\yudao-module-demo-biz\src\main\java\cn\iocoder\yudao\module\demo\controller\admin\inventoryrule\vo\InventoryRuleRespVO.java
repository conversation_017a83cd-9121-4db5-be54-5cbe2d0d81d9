package cn.iocoder.yudao.module.demo.controller.admin.inventoryrule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 盘点规则 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryRuleRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15143")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("规则名称")
    private String ruleName;

    @Schema(description = "规则编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("规则编码")
    private String ruleCode;

    @Schema(description = "判断类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("判断类型")
    private String judgeType;

    @Schema(description = "盘点周期数", requiredMode = Schema.RequiredMode.REQUIRED, example = "26988")
    @ExcelProperty("盘点周期数")
    private Integer inventoryCycleCount;

    @Schema(description = "判断周期类型（日、周、月）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("判断周期类型（日、周、月）")
    private String cycleType;

    @Schema(description = "创建人部门ID", example = "24556")
    @ExcelProperty("创建人部门ID")
    private Long createDeptId;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "最后一次执行时间")
    @ExcelProperty("最后一次执行时间")
    private LocalDateTime lastTime;

    @Schema(description = "规则类型 1-巡检 2-盘点", example = "1")
    private Integer ruleType;

}
