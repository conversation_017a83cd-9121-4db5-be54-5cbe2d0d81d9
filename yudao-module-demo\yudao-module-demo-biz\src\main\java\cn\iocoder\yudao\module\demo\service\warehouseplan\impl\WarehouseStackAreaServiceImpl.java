package cn.iocoder.yudao.module.demo.service.warehouseplan.impl;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.demo.controller.admin.warehouseplan.vo.WarehouseStackAreaReqVO;
import cn.iocoder.yudao.module.demo.controller.admin.warehouseplan.vo.WarehouseStackAreaRespVO;
import cn.iocoder.yudao.module.demo.dal.mysql.warehouseplan.WarehouseStackAreaMapper;
import cn.iocoder.yudao.module.demo.framework.demodatapermission.config.DemoDataPermissionRule;
import cn.iocoder.yudao.module.demo.service.warehouseplan.WarehouseStackAreaService;
import cn.iocoder.yudao.module.demo.util.DataPermissionUtil;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 仓库垛位 Service 实现类
 */
@Service
@Validated
public class WarehouseStackAreaServiceImpl implements WarehouseStackAreaService {

    @Resource
    private WarehouseStackAreaMapper warehouseStackAreaMapper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private DemoDataPermissionRule dataPermissionRule;

    @Override
    public PageResult<WarehouseStackAreaRespVO> getWarehouseStackAreaPage(WarehouseStackAreaReqVO reqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        Long deptId = SecurityFrameworkUtils.getLoginUserDeptId();

        List<Long> deptIdList = DataPermissionUtil.getUserScope(permissionService, dataPermissionRule, userId, deptId);
        reqVO.setDeptIdList(deptIdList);

        // 直接调用Mapper的分页方法
        return warehouseStackAreaMapper.selectWarehouseStackAreaPage(reqVO);
    }
}
