package cn.iocoder.yudao.module.demo.service.bizflownode;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.demo.controller.admin.bizflownode.vo.*;
import cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo.MaterialAcceptRespVO;
import cn.iocoder.yudao.module.demo.controller.admin.materialacceptdetail.vo.MaterialAcceptDetailRespVO;
import cn.iocoder.yudao.module.demo.controller.admin.materialacceptdetail.vo.MaterialAcceptDetailSaveReqVO;
import cn.iocoder.yudao.module.demo.controller.admin.checkstandard.vo.MaterialCheckStandardRespVO;
import cn.iocoder.yudao.module.demo.dal.dataobject.bizflownode.BizFlowNodeDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.dispatchorder.DispatchOrderDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialaccept.MaterialAcceptDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialacceptdetail.MaterialAcceptDetailDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.checkstandard.MaterialCheckStandardDO;
import cn.iocoder.yudao.module.demo.dal.mysql.bizflownode.BizFlowNodeMapper;
import cn.iocoder.yudao.module.demo.dal.mysql.dispatchorder.DispatchOrderMapper;
import cn.iocoder.yudao.module.demo.dal.mysql.materialaccept.MaterialAcceptMapper;
import cn.iocoder.yudao.module.demo.dal.mysql.checkstandard.MaterialCheckStandardMapper;
import cn.iocoder.yudao.module.demo.service.async.AsyncMaterialService;
import cn.iocoder.yudao.module.demo.service.materialaccept.MaterialAcceptService;
import cn.iocoder.yudao.module.demo.service.materialacceptdetail.MaterialAcceptDetailService;
import cn.iocoder.yudao.module.demo.util.ToolUtils;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.service.dept.DeptService;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.demo.enums.ErrorCodeConstants.*;

/**
 * 业务流程节点配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BizFlowNodeServiceImpl implements BizFlowNodeService {

    private static final Logger log = LoggerFactory.getLogger(BizFlowNodeServiceImpl.class);

    @Resource
    private BizFlowNodeMapper bizFlowNodeMapper;

    @Resource
    private MaterialAcceptMapper materialAcceptMapper;

    @Resource
    private MaterialCheckStandardMapper checkStandardMapper;

    @Resource
    private DeptService deptService;

    @Resource
    private MaterialAcceptService materialAcceptService;

    @Resource
    private MaterialAcceptDetailService materialAcceptDetailService;

    @Resource
    private AsyncMaterialService asyncMaterialService;

    @Resource
    private DispatchOrderMapper dispatchOrderMapper;

    @Override
    public Long createBizFlowNode(BizFlowNodeSaveReqVO createReqVO) {
        // 插入
        BizFlowNodeDO bizFlowNode = BeanUtils.toBean(createReqVO, BizFlowNodeDO.class);
        bizFlowNodeMapper.insert(bizFlowNode);
        // 返回
        return bizFlowNode.getId();
    }

    @Override
    @Transactional
    public void updateBizFlowNode(BizFlowNodeSaveReqVO updateReqVO) {
        // 校验存在
        validateBizFlowNodeExists(updateReqVO.getId());

        // 更新节点
        BizFlowNodeDO updateObj = BeanUtils.toBean(updateReqVO, BizFlowNodeDO.class);
        Long businessId = updateReqVO.getBusinessId();

        // 设置审核人
        if (businessId != null) {
            updateObj.setAuditPersonId(SecurityFrameworkUtils.getLoginUserId());
        }
        bizFlowNodeMapper.updateById(updateObj);

        // 处理业务相关逻辑
        if (businessId != null) {
            updateMaterialAcceptStatus(businessId, updateObj.getIsPass(), updateReqVO.getOrderNo());
        }
    }

    /**
     * 更新材料受理状态
     */
    private void updateMaterialAcceptStatus(Long businessId, Integer isPass, Integer orderNo) {
        MaterialAcceptDO materialAccept = materialAcceptMapper.selectById(businessId);

        // 根据审核结果更新状态
        if (isPass == 1 && materialAccept.getFlow().equals(orderNo)) {
            materialAccept.setAuditStatus(4); // 审核通过且流程节点匹配
            //判断是否为物资验收类型 如果是验收类型则新增一条入库信息
            if ("物资验收".equals(materialAccept.getType())){
                // 使用异步服务
                asyncMaterialService.addMaterialAccept(materialAccept);
            }
            if (!"巡检盘点".equals(materialAccept.getType())
                    && !"物资盘点".equals(materialAccept.getType())
                    && !"物资验收".equals(materialAccept.getType())) {
                // 异步发送数据到外部API
                asyncMaterialService.sendMaterialAcceptDataToExternalApi(businessId, materialAccept.getType());
            }
        } else if (isPass == 0) {
            materialAccept.setAuditStatus(3); // 审核驳回
        }

        materialAcceptMapper.updateById(materialAccept);

        handleBindStatus(materialAccept, isPass);
    }

    private void handleBindStatus(MaterialAcceptDO materialAccept, Integer isPass) {
        if (isPass == 0) {
            String relatedNo = materialAccept.getRelatedNo();

            if (StringUtils.isNotBlank(relatedNo)) {
                dispatchOrderMapper.update(Wrappers.<DispatchOrderDO>lambdaUpdate()
                        .eq(DispatchOrderDO::getOrderNo, relatedNo)
                        .set(DispatchOrderDO::getStatus, 0));
            }
        }
    }

    @Override
    public void deleteBizFlowNode(Long id) {
        // 校验存在
        validateBizFlowNodeExists(id);
        // 删除
        bizFlowNodeMapper.deleteById(id);
    }

    private void validateBizFlowNodeExists(Long id) {
        BizFlowNodeDO bizFlowNodeDO = bizFlowNodeMapper.selectById(id);
        if (bizFlowNodeMapper.selectById(id) == null) {
            throw exception(BIZ_FLOW_NODE_NOT_EXISTS);
        }
    }

    @Override
    public BizFlowNodeDO getBizFlowNode(Long id) {
        return bizFlowNodeMapper.selectById(id);
    }

    @Override
    public PageResult<BizFlowNodeDO> getBizFlowNodePage(BizFlowNodePageReqVO pageReqVO) {
        return bizFlowNodeMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BizFlowNodeDO> getBizFlowNodeListByDeptAndBizType(Long deptId, String bizType) {
        return bizFlowNodeMapper.selectListByDeptAndBizType(deptId, bizType);
    }

    @Override
    public List<BizFlowNodeDO> getBizFlowNodeListByCreateDeptAndDeptAndBizType(Long createDeptId, Long deptId, String bizType) {
        return bizFlowNodeMapper.selectListByCreateDeptAndDeptAndBizType(createDeptId, deptId, bizType);
    }

    @Override
    public List<BizFlowNodeDO> getBizFlowNodeListByCreateDeptAndBizType(Long createDeptId, String bizType) {
        return bizFlowNodeMapper.selectListByCreateDeptAndBizType(createDeptId, bizType);
    }

    @Override
    public List<BizFlowNodeDO> getBizFlowNodeListByBusinessId(Long businessId) {
        return bizFlowNodeMapper.selectListByBusinessId(businessId);
    }

    @Override
    public BizFlowNodeDO getBizFlowNodeByOrderNoAndBizTypeAndCreateDeptId(Integer orderNo, String bizType, Long createDeptId) {
        return bizFlowNodeMapper.selectByOrderNoAndBizTypeAndCreateDeptId(orderNo, bizType, createDeptId);
    }

}
