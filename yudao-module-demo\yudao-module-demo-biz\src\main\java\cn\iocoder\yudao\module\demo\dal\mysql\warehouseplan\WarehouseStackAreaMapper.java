package cn.iocoder.yudao.module.demo.dal.mysql.warehouseplan;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.demo.controller.admin.warehouseplan.vo.WarehouseStackAreaReqVO;
import cn.iocoder.yudao.module.demo.controller.admin.warehouseplan.vo.WarehouseStackAreaRespVO;
import cn.iocoder.yudao.module.demo.dal.dataobject.warehouseplan.WarehouseStackAreaDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Mapper
public interface WarehouseStackAreaMapper extends BaseMapperX<WarehouseStackAreaDO> {

    /**
     * 查询仓库和垛位信息
     *
     * @param warehouseId   仓库ID
     * @param stackAreaName 垛位名称，支持模糊查询
     * @return 仓库垛位信息列表
     */
    List<WarehouseStackAreaRespVO> selectWarehouseStackAreaList(
            @Param("warehouseId") Long warehouseId,
            @Param("stackAreaName") String stackAreaName,
            @Param("deptIdList") List<Long> deptIdList);

    /**
     * 分页查询仓库和垛位信息
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    default PageResult<WarehouseStackAreaRespVO> selectWarehouseStackAreaPage(WarehouseStackAreaReqVO reqVO) {
        // 执行查询
        List<WarehouseStackAreaRespVO> list = selectWarehouseStackAreaList(
                reqVO.getWarehouseId(), reqVO.getStackAreaName(), reqVO.getDeptIdList());

        // 如果是导出全部，不需要分页
        if (PageParam.PAGE_SIZE_NONE.equals(reqVO.getPageSize())) {
            return new PageResult<>(list, (long) list.size());
        }

        // 否则执行分页逻辑
        return selectPage(reqVO, list);
    }

    /**
     * 将查询结果集转换为分页结果
     *
     * @param reqVO 分页参数
     * @param list 结果列表
     * @return 分页结果
     */
    default PageResult<WarehouseStackAreaRespVO> selectPage(WarehouseStackAreaReqVO reqVO, List<WarehouseStackAreaRespVO> list) {
        // 计算起始索引和结束索引
        int total = list.size();
        int pageNo = reqVO.getPageNo();
        int pageSize = reqVO.getPageSize();
        int fromIndex = (pageNo - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);

        // 获取当前页数据
        List<WarehouseStackAreaRespVO> pageList = fromIndex < total ?
                list.subList(fromIndex, toIndex) : Collections.emptyList();

        // 返回分页结果
        return new PageResult<WarehouseStackAreaRespVO>(pageList, (long) total);
    }
}
