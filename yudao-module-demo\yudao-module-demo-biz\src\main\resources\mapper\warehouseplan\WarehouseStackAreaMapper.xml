<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.demo.dal.mysql.warehouseplan.WarehouseStackAreaMapper">

    <select id="selectWarehouseStackAreaList" resultType="cn.iocoder.yudao.module.demo.controller.admin.warehouseplan.vo.WarehouseStackAreaRespVO">
        SELECT
            w.id,
            w.warehouse_name,
            w.unit_id,
            d.name AS unitName,
            sa.id AS stackAreaId,
            sa.name AS stackAreaName
        FROM
            T_WAREHOUSE w
        LEFT JOIN
            SYSTEM_DEPT d ON w.unit_id = d.id AND d.status = 0 AND d.deleted = 0
        LEFT JOIN
            T_WAREHOUSE_PLANS wp ON w.id = wp.ck_id AND wp.deleted = 0
        LEFT JOIN
            T_STACK_AREAS sa ON wp.id = sa.plan_id AND sa.deleted = 0
        WHERE
            w.deleted = 0
            <if test="warehouseId != null">
                AND w.id = #{warehouseId}
            </if>
            <if test="stackAreaName != null and stackAreaName != ''">
                AND sa.name LIKE CONCAT('%', #{stackAreaName}, '%')
            </if>
            <if test="deptIdList != null and deptIdList.size() > 0">
                and w.unit_id in
                <foreach item="deptId" collection="deptIdList" separator="," open="(" close=")" index="">
                    #{deptId}
                </foreach>
            </if>
            AND sa.name IS NOT NULL
        ORDER BY w.id, sa.name
    </select>

</mapper>
