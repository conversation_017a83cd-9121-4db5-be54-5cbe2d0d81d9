package cn.iocoder.yudao.module.demo.service.async;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialaccept.MaterialReqDO;
import cn.iocoder.yudao.module.demo.dal.mysql.materialaccept.MaterialReqMapper;
import cn.iocoder.yudao.module.demo.enums.AsyncInterfaceEnum;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.demo.controller.admin.materialaccept.vo.MaterialAcceptRespVO;
import cn.iocoder.yudao.module.demo.controller.admin.materialacceptdetail.vo.MaterialAcceptDetailRespVO;
import cn.iocoder.yudao.module.demo.controller.admin.materialacceptdetail.vo.MaterialAcceptDetailSaveReqVO;
import cn.iocoder.yudao.module.demo.controller.admin.checkstandard.vo.MaterialCheckStandardRespVO;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialaccept.MaterialAcceptDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialacceptdetail.MaterialAcceptDetailDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.checkstandard.MaterialCheckStandardDO;
import cn.iocoder.yudao.module.demo.dal.mysql.materialaccept.MaterialAcceptMapper;
import cn.iocoder.yudao.module.demo.dal.mysql.checkstandard.MaterialCheckStandardMapper;
import cn.iocoder.yudao.module.demo.service.materialaccept.MaterialAcceptService;
import cn.iocoder.yudao.module.demo.service.materialacceptdetail.MaterialAcceptDetailService;
import cn.iocoder.yudao.module.demo.util.ToolUtils;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.service.dept.DeptService;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 异步物资服务类
 *
 * <AUTHOR>
 */
@Service
public class AsyncMaterialService {

    private static final Logger log = LoggerFactory.getLogger(AsyncMaterialService.class);

    @Resource
    private MaterialAcceptMapper materialAcceptMapper;

    @Resource
    private MaterialCheckStandardMapper checkStandardMapper;

    @Resource
    private MaterialAcceptDetailService materialAcceptDetailService;

    @Resource
    private MaterialAcceptService materialAcceptService;

    @Resource
    private DeptService deptService;

    @Resource
    private AdminUserService userService;

    @Resource
    private MaterialReqMapper materialReqMapper;

    /**
     * 异步发送物资验收信息到外部API
     *
     * @param businessId 业务ID
     */
    @Async
    public void sendMaterialAcceptDataToExternalApi(Long businessId, String type) {
        try {
            System.out.println("走进流程");
            // 1. 获取物资验收主表信息
            MaterialAcceptDO materialAccept = materialAcceptMapper.selectById(businessId);
            if (materialAccept == null) {
                log.error("[sendMaterialAcceptDataToExternalApi][业务ID({}) 查询不到物资验收信息]", businessId);
                return;
            }

            // 2. 获取物资验收详情列表
            List<MaterialAcceptDetailDO> details = materialAcceptDetailService.getMaterialAcceptDetailsByAcceptId(businessId);

            // 3. 获取检查标准列表
            List<MaterialCheckStandardDO> checkStandards = checkStandardMapper.selectList(
                    new LambdaQueryWrapperX<MaterialCheckStandardDO>()
                            .eq(MaterialCheckStandardDO::getAcceptId, businessId));

            // 4. 构建完整的响应对象
            MaterialAcceptRespVO respVO = BeanUtils.toBean(materialAccept, MaterialAcceptRespVO.class);
            if ("物资移位".equals(type)) {
                respVO.setOutboundType("移库出库");
            }

            if (StringUtils.isBlank(materialAccept.getContactPhone())) {
                Long userId = SecurityFrameworkUtils.getLoginUserId();
                AdminUserDO user = userService.getUser(userId);
                if (Objects.nonNull(user)) {
                    respVO.setContactPhone(user.getMobile());
                }
            }

            respVO.setDetails(BeanUtils.toBean(details, MaterialAcceptDetailRespVO.class));
            respVO.setCheckStandards(BeanUtils.toBean(checkStandards, MaterialCheckStandardRespVO.class));

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("data", respVO);
            // 5. 构建请求参数并处理数据格式
            String jsonData = JsonUtils.toJsonString(resultMap);
            // 处理JSON数据：将所有字段转换为字符串，null值转换为空字符串
            jsonData = processJsonDataToString(jsonData);

            String apiUrl = AsyncInterfaceEnum.getByType(type);

            // 6. 发送HTTP请求
            HttpResponse response = HttpUtil.createPost(apiUrl)
                    .header("Content-Type", "application/json")
                    .body(jsonData)
                    .execute();

            MaterialReqDO reqDO = new MaterialReqDO();
            reqDO.setReqData(jsonData);
            reqDO.setResData(response.body());
            reqDO.setBusinessId(businessId);
            reqDO.setType(type);

            materialReqMapper.insert(reqDO);

            // 7. 处理响应
            if (response.isOk()) {
                log.info("[sendMaterialAcceptDataToExternalApi][发送物资验收信息成功][业务ID: {}, 响应内容: {}]",
                        businessId, response.body());
            } else {
                log.error("[sendMaterialAcceptDataToExternalApi][发送物资验收信息失败][业务ID: {}, 状态码: {}, 响应内容: {}]",
                        businessId, response.getStatus(), response.body());
            }
        } catch (Exception e) {
            log.error("[sendMaterialAcceptDataToExternalApi][发送物资验收信息异常][业务ID: {}]", businessId, e);
        }
    }

    /**
     * 异步添加物资入库信息
     *
     * @param originalAccept 原始验收单信息
     */
    @Async
    public void addMaterialAccept(final MaterialAcceptDO originalAccept) {
        try {
            // 克隆一个新的物资单对象，避免修改原对象
            MaterialAcceptDO materialAccept = new MaterialAcceptDO();
            // 复制基本属性，但不包括ID和单号
            BeanUtils.copyProperties(originalAccept, materialAccept);
            // 手动清除ID和单号
            materialAccept.setId(null);
            materialAccept.setAcceptNo(null);
            materialAccept.setAuditStatus(null);
            materialAccept.setFlow(0);
            // 设置来源ID为原始验收单ID
            materialAccept.setOriginId(originalAccept.getId());

            Long deptId = originalAccept.getCreateDeptId();
            // 设置新的类型为"物资入库"
            materialAccept.setType("物资入库");

            // 生成入库单号
            String typeStr = "YK";
            DeptDO dept = deptService.getDept(deptId);

            // 获取单号前缀
            String prefix = ToolUtils.generateOrderNoPrefix(dept.getName(), typeStr);
            // 从数据库获取最大序号
            Integer maxSequence = materialAcceptMapper.selectMaxSequenceByPrefix(prefix);
            // 生成验收单号
            String acceptNo = ToolUtils.generateOrderNo(dept.getName(), typeStr, maxSequence);
            materialAccept.setAcceptNo(acceptNo);
            materialAccept.setInboundNo(originalAccept.getAcceptNo());
            // 保存物资入库主表记录
            materialAcceptMapper.insert(materialAccept);

            // 根据原始验收单ID查询出所有关联的详情记录
            List<MaterialAcceptDetailDO> originalDetails = materialAcceptDetailService.getMaterialAcceptDetailsByAcceptId(originalAccept.getId());

            // 判断是否有详情记录
            if (originalDetails != null && !originalDetails.isEmpty()) {
                // 将详情记录转换为VO对象并关联到新的入库单
                List<MaterialAcceptDetailSaveReqVO> newDetails = new ArrayList<>(originalDetails.size());

                for (MaterialAcceptDetailDO detail : originalDetails) {
                    MaterialAcceptDetailSaveReqVO detailVO = BeanUtils.toBean(detail, MaterialAcceptDetailSaveReqVO.class);
                    // 设置新的关联ID，清除原ID
                    detailVO.setId(null);
                    detailVO.setAcceptId(materialAccept.getId());
                    newDetails.add(detailVO);
                }

                // 保存详情列表到新的入库单
                materialAcceptService.saveMaterialAcceptDetails(materialAccept.getId(), newDetails);
            }
        } catch (Exception e) {
            log.error("[addMaterialAccept][添加物资入库信息异常][验收单ID: {}]", originalAccept.getId(), e);
        }
    }

    /**
     * 处理JSON数据，将所有字段转换为字符串类型，null值转换为空字符串
     *
     * @param jsonData 原始JSON字符串
     * @return 处理后的JSON字符串
     */
    private String processJsonDataToString(String jsonData) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(jsonData);
            JsonNode processedNode = convertToStringNode(rootNode, objectMapper);
            return objectMapper.writeValueAsString(processedNode);
        } catch (Exception e) {
            log.error("[processJsonDataToString][处理JSON数据异常]", e);
            return jsonData; // 如果处理失败，返回原始数据
        }
    }

    /**
     * 递归转换JsonNode，将所有值转换为字符串
     *
     * @param node JsonNode节点
     * @param objectMapper ObjectMapper实例
     * @return 转换后的JsonNode
     */
    private JsonNode convertToStringNode(JsonNode node, ObjectMapper objectMapper) {
        if (node.isObject()) {
            ObjectNode objectNode = objectMapper.createObjectNode();
            node.fields().forEachRemaining(entry -> {
                String key = entry.getKey();
                JsonNode value = entry.getValue();
                objectNode.set(key, convertToStringNode(value, objectMapper));
            });
            return objectNode;
        } else if (node.isArray()) {
            ArrayNode arrayNode = objectMapper.createArrayNode();
            for (JsonNode arrayElement : node) {
                arrayNode.add(convertToStringNode(arrayElement, objectMapper));
            }
            return arrayNode;
        } else if (node.isNull()) {
            // null值转换为空字符串
            return objectMapper.getNodeFactory().textNode("");
        } else {
            // 所有其他类型都转换为字符串
            return objectMapper.getNodeFactory().textNode(node.asText());
        }
    }
}
