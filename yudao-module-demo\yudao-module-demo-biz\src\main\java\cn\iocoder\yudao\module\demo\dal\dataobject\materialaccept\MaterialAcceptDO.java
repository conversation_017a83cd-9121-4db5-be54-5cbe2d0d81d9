package cn.iocoder.yudao.module.demo.dal.dataobject.materialaccept;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.demo.dal.dataobject.materialacceptdetail.MaterialAcceptDetailDO;

/**
 * 物资验收信息表 DO
 *
 * <AUTHOR>
 */
@TableName("t_material_accept")
@KeySequence("t_material_accept_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialAcceptDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 验收单号、入库单号、出库单号、物资移位单号、物资盘点单号、巡检单号、维保单号、报废单号、预案单号、隐患单号
     */
    private String acceptNo;
    /**
     * 所在单位/责任部门
     */
    private String unitName;
    /**
     * 单位ID/部门ID
     */
    private Long unitId;
    /**
     * 物资类别/隐患类型（隐患管理）
     */
    private String materialType;
    /**
     * 供货单位
     */
    private String supplier;
    /**
     * 直观检验（验收入库）
     * 维保内容（维修保养）
     */
    private String visualCheck;
    /**
     * 原理检验（验收入库）
     * 维保结果（维修保养）
     */
    private String principleCheck;
    /**
     * 问题及处理意见/报废原因/适用范围/隐患描述（隐患管理）
     */
    private String problemOpinion;
    /**
     * 使用说明
     */
    private String usageDesc;

    /**
     * 验收情况
     */
    private String materialSituation;

    /**
     * 质量检验
     */
    private String qualityInspection;

    /**
     * 验收日期
     */
    private LocalDateTime acceptDate;
    /**
     * 验收经办人
     */
    private String acceptUser;
    /**
     * 签名区
     */
    private String signArea;
    /**
     * 附件ID列表
     */
    private String fileIds;

    /**
     * 物资权属
     */
    private String materialBelong;

    /**
     * 入库单号
     */
    private String inboundNo;

    /**
     * 入库类型
     */
    private String inboundType;

    /**
     * 出库类型
     */
    private String outboundType;

    /**
     * 关联单号
     */
    private String relatedNo;

    /**
     * 入库仓库ID\巡检仓库id
     */
    private Long warehouseId;

    /**
     * 入库仓库名称\巡检仓库名称
     */
    private String warehouseName;

    /**
     * 预计入库时间\预计出库时间
     */
    private LocalDateTime expectedInboundTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 入库经办人/申请人
     */
    private String inboundOperator;

    /**
     * 入库经办人签名区
     */
    private String inboundSignArea;

    /**
     * 当前流程节点
     */
    private Integer flow;

    /**
     * 创建部门
     */
    private Long createDeptId;

    /**
     * 类型 1:物资验收  2：物资入库
     */
    private String type;

    /**
     * 物资分类
     */
    private String materialClass;

    /**
     * 物资规格
     */
    private String spec;

    /**
     * 单位
     */
    private String unit;

    /**
     * 数量/移动数量/报废数量
     */
    private Integer quantity;

    /**
     * 原id
     */
    private Long originId;

    /**
     * 调令ID
     */
    private Long dispatchOrderId;

    /**
     * 调令单号
     */
    private String dispatchOrderNo;

    /**
     * 接收单位
     */
    private String receiveUnit;

    /**
     * 接收单位ID
     */
    private Long receiveId;

    /**
     * 单位地址
     */
    private String unitAddress;

    /**
     * 联系人\巡检人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 物资名称\预案名称
     */
    private String materialName;

    /**
     * 原货位
     */
    private String originalLocation;

    /**
     * 垛位 /巡检垛位
     */
    private String location;

    /**
     * 垛位ID
     */
    private Long locationId;

    /**
     * 目标货位
     */
    private String newLocation;

    /**
     * 物资编码
     */
    private String materialCode;

    /**
     * 盘点类型/处理方式（隐患管理）
     */
    private String checkType;

    /**
     * 盘点范围/隐患等级（隐患管理）
     */
    private String checkRange;

    /**
     * 计划开始时间（）
     */
    private LocalDateTime planTime;

    /**
     * 预案等级
     */
    private Integer level;

    /**
     * 审核状态 0：待提交1：待审核  2：待上级审核  3：审核驳回 4：审核通过
     */
    private Integer auditStatus;

    /**
     * 巡检状态 1：待执行  2：执行中  3：执行通过
     */
    private Integer inspectionStatus;

    /**
     * 生产厂家名称
     */
    private String proFactoryName;
    /**
     * 生产厂家ID
     */
    private Long proFactoryId;

    /**
     * 物资种类
     */
    private Integer types;

    /**
     * 巡检仓库IDs
     */
    private String inspectionWarehouseIds;

    /**
     * 巡检类型
     */
    private String inspectionType;

    /**
     * 第三方传过来的文件集合
     */
    private String fileList;

    /**
     * 第三方结果标识
     */
    private String thirdResultFlag;

    private String dataSource;

    /**
     * 处理措施/上报原因
     */
    private String handleContent;

    private LocalDateTime completeTime;
}
